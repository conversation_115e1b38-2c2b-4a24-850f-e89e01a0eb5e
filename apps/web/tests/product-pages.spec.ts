import { expect, test } from "@playwright/test";

test.describe("Product Pages", () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication - this would need to be adjusted based on your auth setup
    // For now, we'll just test that the pages load without authentication errors
  });

  test("should load products page with new layout", async ({ page }) => {
    // Navigate to products page
    await page.goto("/app/products");
    
    // Check if the page loads (might redirect to login, which is expected)
    // We're mainly testing that there are no JavaScript errors
    const errors: string[] = [];
    page.on('pageerror', (error) => {
      errors.push(error.message);
    });
    
    // Wait a bit to catch any immediate errors
    await page.waitForTimeout(1000);
    
    // Verify no JavaScript errors occurred
    expect(errors).toHaveLength(0);
  });

  test("should load product creation page with new layout", async ({ page }) => {
    // Navigate to product creation page
    await page.goto("/app/products/new");
    
    // Check if the page loads (might redirect to login, which is expected)
    const errors: string[] = [];
    page.on('pageerror', (error) => {
      errors.push(error.message);
    });
    
    // Wait a bit to catch any immediate errors
    await page.waitForTimeout(1000);
    
    // Verify no JavaScript errors occurred
    expect(errors).toHaveLength(0);
  });

  test("breadcrumb component should render without errors", async ({ page }) => {
    // Create a simple test page to verify breadcrumb component
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <head>
          <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
          <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
        </head>
        <body>
          <div id="root"></div>
          <script>
            // Simple test to verify breadcrumb structure
            const breadcrumbHTML = \`
              <nav aria-label="breadcrumb">
                <ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                  <li class="inline-flex items-center gap-1.5">
                    <a href="/app/products" class="transition-colors hover:text-foreground">Produtos</a>
                  </li>
                  <li role="presentation" aria-hidden="true">
                    <svg class="h-4 w-4"><path d="m9 18 6-6-6-6"/></svg>
                  </li>
                  <li class="inline-flex items-center gap-1.5">
                    <span role="link" aria-disabled="true" aria-current="page" class="font-normal text-foreground">Criar Produto</span>
                  </li>
                </ol>
              </nav>
            \`;
            document.getElementById('root').innerHTML = breadcrumbHTML;
          </script>
        </body>
      </html>
    `);
    
    // Verify breadcrumb elements are present
    await expect(page.locator('nav[aria-label="breadcrumb"]')).toBeVisible();
    await expect(page.locator('ol')).toBeVisible();
    await expect(page.locator('li')).toHaveCount(3); // Two items + one separator
  });

  test("page layout component structure should be valid", async ({ page }) => {
    // Test the basic HTML structure that PageLayout would generate
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Test Page Layout</title>
        </head>
        <body>
          <div class="w-full max-w-none">
            <div class="space-y-6">
              <!-- Breadcrumbs -->
              <nav aria-label="breadcrumb">
                <ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                  <li class="inline-flex items-center gap-1.5">
                    <a href="/app">Dashboard</a>
                  </li>
                  <li role="presentation" aria-hidden="true">></li>
                  <li class="inline-flex items-center gap-1.5">
                    <span role="link" aria-disabled="true" aria-current="page">Produtos</span>
                  </li>
                </ol>
              </nav>
              
              <!-- Header Section -->
              <div class="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
                <div class="space-y-1">
                  <h1 class="text-3xl font-bold tracking-tight">Meus Produtos</h1>
                  <p class="text-muted-foreground">Gerencie seus produtos digitais</p>
                </div>
                <div class="flex shrink-0">
                  <button class="btn-primary">Novo Produto</button>
                </div>
              </div>
              
              <!-- Content Area -->
              <div class="flex-1">
                <p>Content goes here</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `);
    
    // Verify the main structure elements
    await expect(page.locator('h1')).toContainText('Meus Produtos');
    await expect(page.locator('p').first()).toContainText('Gerencie seus produtos digitais');
    await expect(page.locator('nav[aria-label="breadcrumb"]')).toBeVisible();
    await expect(page.locator('button')).toContainText('Novo Produto');
  });
});
