'use client';

import { cn } from '@ui/lib';
import { forwardRef, useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/dropdown-menu';

interface Country {
  code: string;
  name: string;
  flag: string;
  dialCode: string;
}

const countries: Country[] = [
  { code: 'BR', name: 'Brasil', flag: '🇧🇷', dialCode: '+55' },
  { code: 'US', name: 'Estados Unidos', flag: '🇺🇸', dialCode: '+1' },
  { code: 'PT', name: 'Portugal', flag: '🇵🇹', dialCode: '+351' },
  { code: 'AR', name: 'Argentina', flag: '🇦🇷', dialCode: '+54' },
  { code: 'CL', name: 'Chile', flag: '🇨🇱', dialCode: '+56' },
  { code: 'CO', name: '<PERSON><PERSON><PERSON><PERSON>', flag: '🇨🇴', dialCode: '+57' },
  { code: 'MX', name: 'México', flag: '🇲🇽', dialCode: '+52' },
  { code: 'ES', name: 'Espanha', flag: '🇪🇸', dialCode: '+34' },
  { code: 'FR', name: 'França', flag: '🇫🇷', dialCode: '+33' },
  { code: 'DE', name: 'Alemanha', flag: '🇩🇪', dialCode: '+49' },
  { code: 'IT', name: 'Itália', flag: '🇮🇹', dialCode: '+39' },
  { code: 'GB', name: 'Reino Unido', flag: '🇬🇧', dialCode: '+44' },
];

interface PhoneInputWithFlagProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value: string;
  onChange?: (value: string) => void;
  error?: boolean;
  errorMessage?: string;
}

export const PhoneInputWithFlag = forwardRef<
  HTMLInputElement,
  PhoneInputWithFlagProps
>(({ className, value, onChange, error, errorMessage, ...props }, ref) => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(countries[0]);
  const [phoneNumber, setPhoneNumber] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // Sincronizar a ref interna com a ref externa
  useEffect(() => {
    if (ref && typeof ref === 'function') {
      ref(inputRef.current);
    } else if (ref && inputRef.current) {
      ref.current = inputRef.current;
    }
  }, [ref]);

  // Parsear o valor inicial para extrair país e número
  useEffect(() => {
    if (value) {
      const country = countries.find(c => value.startsWith(c.dialCode));
      if (country) {
        setSelectedCountry(country);
        setPhoneNumber(value.replace(country.dialCode, '').trim());
      } else {
        setPhoneNumber(value);
      }
    }
  }, [value]);

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);
    const fullNumber = country.dialCode + (phoneNumber || '');
    onChange?.(fullNumber);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPhone = e.target.value.replace(/\D/g, '');
    setPhoneNumber(newPhone);
    const fullNumber = selectedCountry.dialCode + newPhone;
    onChange?.(fullNumber);
  };

  const formatPhoneNumber = (phone: string) => {
    const digits = phone.replace(/\D/g, '');
    if (digits.length <= 2) return digits;
    if (digits.length <= 6) return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
    if (digits.length <= 10) return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)}-${digits.slice(6)}`;
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7, 11)}`;
  };

  return (
    <div className=" ">
      <div className="relative">
        <div className="flex rounded-md border border-input bg-card shadow-xs focus-within:ring-1 focus-within:border-ring focus-within:ring-ring">
          {/* Seletor de País */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-10 px-3 py-2 text-left border-0 rounded-r-none focus:ring-0 focus:ring-offset-0 hover:bg-muted/50 transition-colors"
                type="button"
              >
                <div className="flex items-center gap-2">
                  <span className="text-lg">{selectedCountry.flag}</span>
                  <span className="text-sm font-medium">{selectedCountry.dialCode}</span>
                  <ChevronDown className="h-4 w-4 opacity-50" />
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-64 max-h-60 overflow-y-auto">
              {countries.map((country) => (
                <DropdownMenuItem
                  key={country.code}
                  onClick={() => handleCountryChange(country)}
                  className="flex items-center gap-3 cursor-pointer"
                >
                  <span className="text-lg">{country.flag}</span>
                  <div className="flex flex-col">
                    <span className="font-medium">{country.name}</span>
                    <span className="text-sm text-muted-foreground">{country.dialCode}</span>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Input de Telefone */}
          <Input
            ref={inputRef}
            type="tel"
            value={formatPhoneNumber(phoneNumber)}
            onChange={handlePhoneChange}
            placeholder="(00) 00000-0000"
            className={cn(
              'flex-1 h-10 border-0 rounded-l-none focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent shadow-none',
              error && 'border-destructive',
              className
            )}
            {...props}
          />
        </div>
      </div>
      {error && errorMessage && (
        <p className="text-sm font-medium text-destructive">{errorMessage}</p>
      )}
    </div>
  );
});

PhoneInputWithFlag.displayName = 'PhoneInputWithFlag';
