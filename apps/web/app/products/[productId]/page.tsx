import { notFound } from "next/navigation";
import { db } from "@repo/database/prisma/client";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Separator } from "@ui/components/separator";
import {
  ShoppingCartIcon,
  StarIcon,
  UsersIcon,
  DollarSignIcon,
  CheckIcon,
  ArrowRightIcon,
  ShieldIcon,
  ClockIcon,
  HeadphonesIcon
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface ProductPageProps {
  params: Promise<{
    productId: string;
  }>;
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { productId } = await params;

  // Buscar produto publicado
  const product = await db.product.findFirst({
    where: {
      id: productId,
      status: 'PUBLISHED',
    },
    include: {
      organization: {
        select: {
          name: true,
          slug: true,
        }
      },
      category: {
        select: {
          name: true,
        }
      },
      _count: {
        select: {
          orders: true,
          enrollments: true,
        },
      },
    },
  });

  if (!product) {
    notFound();
  }

  const formatCurrency = (cents: number, currency: string = 'BRL') => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency,
    }).format(cents / 100);
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'COURSE':
        return 'Curso';
      case 'EBOOK':
        return 'E-book';
      case 'MENTORING':
        return 'Mentoria';
      case 'SUBSCRIPTION':
        return 'Assinatura';
      case 'BUNDLE':
        return 'Pacote';
      default:
        return type;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'COURSE':
        return '🎓';
      case 'EBOOK':
        return '📚';
      case 'MENTORING':
        return '👥';
      case 'SUBSCRIPTION':
        return '🔄';
      case 'BUNDLE':
        return '📦';
      default:
        return '📄';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Header */}
      <header className="bg-white dark:bg-slate-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-slate-900 dark:text-white">
                {product.organization.name}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/auth/login">
                  Entrar
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid gap-8 lg:grid-cols-12">
          {/* Conteúdo Principal */}
          <div className="lg:col-span-8 space-y-8">
            {/* Imagem do Produto */}
            <div className="relative w-full h-64 sm:h-80 lg:h-96 rounded-xl overflow-hidden bg-slate-100 dark:bg-slate-700">
              {product.thumbnail ? (
                <Image
                  src={product.thumbnail}
                  alt={product.name}
                  fill
                  className="object-cover"
                  priority
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-6xl mb-4">{getTypeIcon(product.type)}</div>
                    <p className="text-slate-500 dark:text-slate-400">Sem imagem</p>
                  </div>
                </div>
              )}
            </div>

            {/* Informações do Produto */}
            <div className="space-y-6">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline" className="text-sm">
                    {getTypeLabel(product.type)}
                  </Badge>
                  {product.category && (
                    <Badge variant="secondary" className="text-sm">
                      {product.category.name}
                    </Badge>
                  )}
                </div>
                <h1 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                  {product.name}
                </h1>
                {product.description && (
                  <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed">
                    {product.description}
                  </p>
                )}
              </div>

              {/* Estatísticas */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-4 bg-white dark:bg-slate-800 rounded-lg border">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full mx-auto mb-2">
                    <UsersIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-slate-900 dark:text-white">
                    {product._count?.enrollments || 0}
                  </p>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Alunos</p>
                </div>
                <div className="text-center p-4 bg-white dark:bg-slate-800 rounded-lg border">
                  <div className="flex items-center justify-center w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full mx-auto mb-2">
                    <DollarSignIcon className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <p className="text-2xl font-bold text-slate-900 dark:text-white">
                    {product._count?.orders || 0}
                  </p>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Vendas</p>
                </div>
                <div className="text-center p-4 bg-white dark:bg-slate-800 rounded-lg border">
                  <div className="flex items-center justify-center w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-full mx-auto mb-2">
                    <StarIcon className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <p className="text-2xl font-bold text-slate-900 dark:text-white">4.8</p>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Avaliação</p>
                </div>
              </div>

              {/* Descrição Detalhada */}
              {product.shortDescription && (
                <div className="bg-white dark:bg-slate-800 rounded-lg p-6 border">
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">
                    Sobre este produto
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                    {product.shortDescription}
                  </p>
                </div>
              )}

              {/* Benefícios */}
              <div className="bg-white dark:bg-slate-800 rounded-lg p-6 border">
                <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
                  O que você vai aprender
                </h3>
                <div className="grid gap-3 sm:grid-cols-2">
                  {[
                    "Conteúdo exclusivo e atualizado",
                    "Acesso vitalício ao material",
                    "Suporte especializado",
                    "Certificado de conclusão",
                    "Comunidade de alunos",
                    "Atualizações gratuitas"
                  ].map((benefit, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <CheckIcon className="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0" />
                      <span className="text-slate-600 dark:text-slate-300">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar de Compra */}
          <div className="lg:col-span-4">
            <div className="sticky top-8">
              <Card className="shadow-xl border-0 bg-white dark:bg-slate-800">
                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white">
                    {formatCurrency(product.priceCents, product.currency)}
                  </CardTitle>
                  {product.comparePriceCents && (
                    <CardDescription className="text-lg">
                      <span className="line-through text-slate-500 dark:text-slate-400">
                        {formatCurrency(product.comparePriceCents, product.currency)}
                      </span>
                      <span className="ml-2 text-green-600 dark:text-green-400 font-semibold">
                        Economia de {formatCurrency(product.comparePriceCents - product.priceCents, product.currency)}
                      </span>
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Botão de Compra */}
                  <Button
                    size="lg"
                    className="w-full h-12 text-lg font-semibold"
                    asChild
                  >
                    <Link href={`/checkout/${productId}`}>
                      <ShoppingCartIcon className="w-5 h-5 mr-2" />
                      Comprar Agora
                      <ArrowRightIcon className="w-5 h-5 ml-2" />
                    </Link>
                  </Button>

                  {/* Garantias */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 text-sm text-slate-600 dark:text-slate-300">
                      <ShieldIcon className="w-4 h-4 text-green-600 dark:text-green-400" />
                      <span>Garantia de 7 dias</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm text-slate-600 dark:text-slate-300">
                      <ClockIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      <span>Acesso imediato</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm text-slate-600 dark:text-slate-300">
                      <HeadphonesIcon className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                      <span>Suporte 24/7</span>
                    </div>
                  </div>

                  <Separator />

                  {/* Informações de Pagamento */}
                  <div className="text-center">
                    <p className="text-sm text-slate-500 dark:text-slate-400 mb-2">
                      Pagamento 100% seguro
                    </p>
                    <div className="flex justify-center items-center gap-4">
                      <div className="text-xs text-slate-400">Cartão de Crédito</div>
                      <div className="text-xs text-slate-400">PIX</div>
                      <div className="text-xs text-slate-400">Boleto</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white dark:bg-slate-800 border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-slate-500 dark:text-slate-400">
            <p>&copy; 2024 {product.organization.name}. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
