import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import {
	SearchIcon,
	FilterIcon,
	PlusIcon,
	MoreHorizontalIcon,
	ActivityIcon,
	CalendarIcon,
	DollarSignIcon,
	CheckCircleIcon,
	XCircleIcon,
	ClockIcon,
	AlertCircleIcon,
	BuildingIcon,
	UserIcon,
	RotateCcwIcon
} from "lucide-react";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";
import { db } from "@repo/database/prisma/client";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";

interface SearchParams {
	query?: string;
	status?: string;
	organization?: string;
}

export default async function AdminRefundsPage({
	searchParams,
}: {
	searchParams: Promise<SearchParams>;
}) {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	const { query, status, organization } = await searchParams;

	// Buscar estornos (simulando dados - você precisará criar a tabela de estornos)
	const refunds = [
		{
			id: "ref_001",
			transactionId: "txn_001",
			organizationId: "org_001",
			organizationName: "Academia Digital",
			amountCents: 15000, // R$ 150,00
			status: "PENDING",
			createdAt: new Date("2024-01-15T10:30:00Z"),
			requestedBy: "João Silva",
			reason: "Produto defeituoso",
			description: "Cliente solicitou estorno devido a defeito no produto"
		},
		{
			id: "ref_002",
			transactionId: "txn_002",
			organizationId: "org_002",
			organizationName: "Tech Solutions",
			amountCents: 50000, // R$ 500,00
			status: "COMPLETED",
			createdAt: new Date("2024-01-14T14:20:00Z"),
			requestedBy: "Maria Santos",
			reason: "Cancelamento de serviço",
			description: "Cliente cancelou o serviço antes do prazo"
		},
		{
			id: "ref_003",
			transactionId: "txn_003",
			organizationId: "org_003",
			organizationName: "Digital Agency",
			amountCents: 25000, // R$ 250,00
			status: "REJECTED",
			createdAt: new Date("2024-01-13T09:15:00Z"),
			requestedBy: "Carlos Oliveira",
			reason: "Arrependimento",
			description: "Cliente se arrependeu da compra"
		}
	];

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "COMPLETED":
				return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200"><CheckCircleIcon className="w-3 h-3 mr-1" />Processado</Badge>;
			case "PENDING":
				return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200"><ClockIcon className="w-3 h-3 mr-1" />Pendente</Badge>;
			case "PROCESSING":
				return <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200"><ClockIcon className="w-3 h-3 mr-1" />Processando</Badge>;
			case "REJECTED":
				return <Badge variant="destructive"><XCircleIcon className="w-3 h-3 mr-1" />Rejeitado</Badge>;
			case "CANCELLED":
				return <Badge variant="outline"><AlertCircleIcon className="w-3 h-3 mr-1" />Cancelado</Badge>;
			default:
				return <Badge variant="outline">{status}</Badge>;
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Estornos</h1>
					<p className="text-muted-foreground">
						Gerencie todos os estornos solicitados. Total: {refunds.length}
					</p>
				</div>
				<Button>
					<PlusIcon className="mr-2 h-4 w-4" />
					Novo Estorno
				</Button>
			</div>

			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar estornos..."
						className="pl-10"
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Estornado</CardTitle>
						<RotateCcwIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">R$ 45.230,00</div>
						<p className="text-xs text-muted-foreground">+5% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Estornos Processados</CardTitle>
						<CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">23</div>
						<p className="text-xs text-muted-foreground">+12% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pendentes</CardTitle>
						<ClockIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">8</div>
						<p className="text-xs text-muted-foreground">-2 vs ontem</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Taxa de Aprovação</CardTitle>
						<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">78.5%</div>
						<p className="text-xs text-muted-foreground">+3.2% vs mês anterior</p>
					</CardContent>
				</Card>
			</div>

			<div className="grid gap-4">
				{refunds.map((refund) => (
					<Card key={refund.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
										<RotateCcwIcon className="h-5 w-5 text-muted-foreground" />
									</div>
									<div>
										<CardTitle className="text-lg">
											{refund.organizationName}
										</CardTitle>
										<CardDescription className="flex items-center gap-2">
											<CalendarIcon className="h-4 w-4" />
											{new Date(refund.createdAt).toLocaleDateString("pt-BR")} às{" "}
											{new Date(refund.createdAt).toLocaleTimeString("pt-BR", {
												hour: "2-digit",
												minute: "2-digit"
											})}
										</CardDescription>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<div className="text-right">
										<div className="text-lg font-bold">
											R$ {(refund.amountCents / 100).toLocaleString("pt-BR", {
												minimumFractionDigits: 2
											})}
										</div>
										<div className="text-sm text-muted-foreground">
											{refund.reason}
										</div>
									</div>
									{getStatusBadge(refund.status)}
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="outline" size="sm">
												<MoreHorizontalIcon className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem>Visualizar</DropdownMenuItem>
											<DropdownMenuItem>Aprovar</DropdownMenuItem>
											<DropdownMenuItem>Rejeitar</DropdownMenuItem>
											<DropdownMenuItem className="text-destructive">
												Cancelar
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-4">
								<div className="flex items-center space-x-2">
									<UserIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Solicitado por</p>
										<p className="text-xs text-muted-foreground">
											{refund.requestedBy}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<BuildingIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">ID da Transação</p>
										<p className="text-xs text-muted-foreground font-mono">
											{refund.transactionId}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Descrição</p>
										<p className="text-xs text-muted-foreground">
											{refund.description}
										</p>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
