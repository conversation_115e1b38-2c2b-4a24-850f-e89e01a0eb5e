import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import {
  SettingsIcon,
  DatabaseIcon,
  ShieldIcon,
  MailIcon,
  CreditCardIcon,
  BellIcon,
  SaveIcon,
  ServerIcon,
  UsersIcon,
  BuildingIcon,
} from "lucide-react";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";

export default async function BackofficeSettingsPage() {
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  if (!isAdmin(session.user)) {
    redirect("/app");
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Configurações do Sistema</h1>
        <p className="text-muted-foreground">
          Configure as definições globais da plataforma
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Configurações de Organizações */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BuildingIcon className="h-5 w-5" />
              Organizações
            </CardTitle>
            <CardDescription>
              Configurações relacionadas a organizações e tenants.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Organizações Habilitadas</Label>
                <p className="text-sm text-muted-foreground">
                  Permitir múltiplas organizações.
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Organização Obrigatória</Label>
                <p className="text-sm text-muted-foreground">
                  Usuários devem pertencer a uma organização.
                </p>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Criação por Usuários</Label>
                <p className="text-sm text-muted-foreground">
                  Usuários podem criar organizações.
                </p>
              </div>
              <Switch defaultChecked />
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Usuários */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UsersIcon className="h-5 w-5" />
              Usuários
            </CardTitle>
            <CardDescription>
              Configurações de registro e gestão de usuários.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Registro Público</Label>
                <p className="text-sm text-muted-foreground">
                  Permitir registro público de usuários.
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Verificação de Email</Label>
                <p className="text-sm text-muted-foreground">
                  Exigir verificação de email no registro.
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="space-y-2">
              <Label>Limite de Usuários por Organização</Label>
              <Input type="number" defaultValue="50" />
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Pagamentos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCardIcon className="h-5 w-5" />
              Pagamentos
            </CardTitle>
            <CardDescription>
              Configurações globais de pagamentos e cobrança.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>PIX Habilitado</Label>
                <p className="text-sm text-muted-foreground">
                  Permitir pagamentos via PIX.
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Cartão de Crédito</Label>
                <p className="text-sm text-muted-foreground">
                  Permitir pagamentos com cartão.
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="space-y-2">
              <Label>Taxa de Transação (%)</Label>
              <Input type="number" step="0.01" defaultValue="2.99" />
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Email */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MailIcon className="h-5 w-5" />
              Email
            </CardTitle>
            <CardDescription>
              Configurações de envio de emails do sistema.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Provedor de Email</Label>
              <Select defaultValue="smtp">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="smtp">SMTP</SelectItem>
                  <SelectItem value="sendgrid">SendGrid</SelectItem>
                  <SelectItem value="mailgun">Mailgun</SelectItem>
                  <SelectItem value="ses">Amazon SES</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Email Remetente</Label>
              <Input type="email" defaultValue="<EMAIL>" />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Emails Transacionais</Label>
                <p className="text-sm text-muted-foreground">
                  Enviar emails de confirmação e notificações.
                </p>
              </div>
              <Switch defaultChecked />
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Segurança */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShieldIcon className="h-5 w-5" />
              Segurança
            </CardTitle>
            <CardDescription>
              Configurações de segurança e compliance.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Autenticação 2FA</Label>
                <p className="text-sm text-muted-foreground">
                  Exigir autenticação de dois fatores.
                </p>
              </div>
              <Switch />
            </div>
            <div className="space-y-2">
              <Label>Tempo de Sessão (minutos)</Label>
              <Input type="number" defaultValue="480" />
            </div>
            <div className="space-y-2">
              <Label>Política de Senhas</Label>
              <Select defaultValue="medium">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Baixa</SelectItem>
                  <SelectItem value="medium">Média</SelectItem>
                  <SelectItem value="high">Alta</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Configurações do Sistema */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ServerIcon className="h-5 w-5" />
              Sistema
            </CardTitle>
            <CardDescription>
              Configurações técnicas e de performance.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Modo de Manutenção</Label>
                <p className="text-sm text-muted-foreground">
                  Ativar modo de manutenção do sistema.
                </p>
              </div>
              <Switch />
            </div>
            <div className="space-y-2">
              <Label>Logs de Debug</Label>
              <Select defaultValue="error">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="debug">Debug</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="warn">Warning</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Backup Automático</Label>
              <Select defaultValue="daily">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hourly">A cada hora</SelectItem>
                  <SelectItem value="daily">Diário</SelectItem>
                  <SelectItem value="weekly">Semanal</SelectItem>
                  <SelectItem value="monthly">Mensal</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Botão de Salvar */}
      <div className="flex justify-end">
        <Button className="min-w-32">
          <SaveIcon className="h-4 w-4 mr-2" />
          Salvar Configurações
        </Button>
      </div>
    </div>
  );
}
