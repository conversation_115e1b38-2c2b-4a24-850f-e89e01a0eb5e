import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  SearchIcon,
  FilterIcon,
  DownloadIcon,
  RefreshCcwIcon,
  AlertTriangleIcon,
  InfoIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from "lucide-react";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";

export default async function BackofficeLogsPage() {
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  if (!isAdmin(session.user)) {
    redirect("/app");
  }

  // Mock data - in real app this would come from database
  const logs = [
    {
      id: "log-001",
      timestamp: "2025-01-06 14:30:25",
      level: "INFO",
      action: "USER_LOGIN",
      user: "João Silva",
      organization: "Academia Digital",
      details: "Login realizado com sucesso",
      ip: "*************",
    },
    {
      id: "log-002",
      timestamp: "2025-01-06 14:28:15",
      level: "SUCCESS",
      action: "ORGANIZATION_CREATED",
      user: "Admin System",
      organization: "Nova Empresa LTDA",
      details: "Organização criada com sucesso",
      ip: "********",
    },
    {
      id: "log-003",
      timestamp: "2025-01-06 14:25:10",
      level: "WARNING",
      action: "PAYMENT_FAILED",
      user: "Maria Santos",
      organization: "Loja Virtual",
      details: "Falha no processamento do pagamento - Cartão recusado",
      ip: "************",
    },
    {
      id: "log-004",
      timestamp: "2025-01-06 14:20:05",
      level: "ERROR",
      action: "API_ERROR",
      user: "Sistema",
      organization: "N/A",
      details: "Erro na integração com gateway de pagamento",
      ip: "127.0.0.1",
    },
    {
      id: "log-005",
      timestamp: "2025-01-06 14:15:30",
      level: "INFO",
      action: "USER_LOGOUT",
      user: "Pedro Costa",
      organization: "Consultoria Tech",
      details: "Logout realizado pelo usuário",
      ip: "*************",
    },
  ];

  const getLogIcon = (level: string) => {
    switch (level) {
      case "SUCCESS":
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case "INFO":
        return <InfoIcon className="h-4 w-4 text-blue-500" />;
      case "WARNING":
        return <AlertTriangleIcon className="h-4 w-4 text-yellow-500" />;
      case "ERROR":
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getLogBadgeVariant = (level: string) => {
    switch (level) {
      case "SUCCESS":
        return "success";
      case "INFO":
        return "info";
      case "WARNING":
        return "warning";
      case "ERROR":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Logs do Sistema</h1>
          <p className="text-muted-foreground">
            Monitore atividades e eventos do sistema
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RefreshCcwIcon className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
          <Button variant="outline" size="sm">
            <DownloadIcon className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Buscar logs..."
                className="pl-10"
              />
            </div>
            <Select defaultValue="all">
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os níveis</SelectItem>
                <SelectItem value="success">Sucesso</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="warning">Aviso</SelectItem>
                <SelectItem value="error">Erro</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="all-actions">
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-actions">Todas as ações</SelectItem>
                <SelectItem value="user-login">Login de usuário</SelectItem>
                <SelectItem value="user-logout">Logout de usuário</SelectItem>
                <SelectItem value="org-created">Organização criada</SelectItem>
                <SelectItem value="payment-failed">Pagamento falhou</SelectItem>
                <SelectItem value="api-error">Erro de API</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <FilterIcon className="h-4 w-4 mr-2" />
              Filtros Avançados
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Estatísticas Rápidas */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Logs</CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,247</div>
            <p className="text-xs text-muted-foreground">Últimas 24 horas</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Erros</CardTitle>
            <XCircleIcon className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">23</div>
            <p className="text-xs text-muted-foreground">Últimas 24 horas</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avisos</CardTitle>
            <AlertTriangleIcon className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">87</div>
            <p className="text-xs text-muted-foreground">Últimas 24 horas</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sucessos</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">1,137</div>
            <p className="text-xs text-muted-foreground">Últimas 24 horas</p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Logs Recentes</CardTitle>
          <CardDescription>
            Últimas atividades registradas no sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {logs.map((log) => (
              <div
                key={log.id}
                className="flex items-start space-x-4 p-4 rounded-lg border bg-muted/50"
              >
                <div className="flex-shrink-0 mt-1">
                  {getLogIcon(log.level)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant={getLogBadgeVariant(log.level) as any}>
                      {log.level}
                    </Badge>
                    <span className="text-sm font-medium">{log.action}</span>
                    <span className="text-xs text-muted-foreground">
                      {log.timestamp}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {log.details}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>Usuário: {log.user}</span>
                    <span>Organização: {log.organization}</span>
                    <span>IP: {log.ip}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
