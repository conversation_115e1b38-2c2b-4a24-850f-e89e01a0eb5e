import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import {
	SearchIcon,
	FilterIcon,
	PlusIcon,
	MoreHorizontalIcon,
	TrendingUpIcon,
	CalendarIcon,
	DollarSignIcon,
	CheckCircleIcon,
	XCircleIcon,
	ClockIcon,
	AlertCircleIcon,
	BuildingIcon,
	UserIcon
} from "lucide-react";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";
import { db } from "@repo/database/prisma/client";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";

interface SearchParams {
	query?: string;
	status?: string;
	organization?: string;
}

export default async function AdminWithdrawalsPage({
	searchParams,
}: {
	searchParams: Promise<SearchParams>;
}) {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	const { query, status, organization } = await searchParams;

	// Buscar saques (simulando dados - você precisará criar a tabela de saques)
	const withdrawals = [
		{
			id: "wth_001",
			organizationId: "org_001",
			organizationName: "Academia Digital",
			amountCents: 500000, // R$ 5.000,00
			status: "PENDING",
			createdAt: new Date("2024-01-15T10:30:00Z"),
			requestedBy: "João Silva",
			bankAccount: "Banco do Brasil ****1234",
			description: "Saque mensal - Janeiro 2024"
		},
		{
			id: "wth_002",
			organizationId: "org_002",
			organizationName: "Tech Solutions",
			amountCents: 250000, // R$ 2.500,00
			status: "COMPLETED",
			createdAt: new Date("2024-01-14T14:20:00Z"),
			requestedBy: "Maria Santos",
			bankAccount: "Itaú ****5678",
			description: "Saque semanal"
		},
		{
			id: "wth_003",
			organizationId: "org_003",
			organizationName: "Digital Agency",
			amountCents: 750000, // R$ 7.500,00
			status: "FAILED",
			createdAt: new Date("2024-01-13T09:15:00Z"),
			requestedBy: "Carlos Oliveira",
			bankAccount: "Bradesco ****9012",
			description: "Saque mensal - Dezembro 2023"
		}
	];

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "COMPLETED":
				return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200"><CheckCircleIcon className="w-3 h-3 mr-1" />Processado</Badge>;
			case "PENDING":
				return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200"><ClockIcon className="w-3 h-3 mr-1" />Pendente</Badge>;
			case "PROCESSING":
				return <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200"><ClockIcon className="w-3 h-3 mr-1" />Processando</Badge>;
			case "FAILED":
				return <Badge variant="destructive"><XCircleIcon className="w-3 h-3 mr-1" />Falhou</Badge>;
			case "CANCELLED":
				return <Badge variant="outline"><AlertCircleIcon className="w-3 h-3 mr-1" />Cancelado</Badge>;
			default:
				return <Badge variant="outline">{status}</Badge>;
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Saques</h1>
					<p className="text-muted-foreground">
						Gerencie todos os saques solicitados pelas organizações. Total: {withdrawals.length}
					</p>
				</div>
				<Button>
					<PlusIcon className="mr-2 h-4 w-4" />
					Processar Saque
				</Button>
			</div>

			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar saques..."
						className="pl-10"
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Solicitado</CardTitle>
						<TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">R$ 1.500.000,00</div>
						<p className="text-xs text-muted-foreground">+15% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Saques Processados</CardTitle>
						<CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">45</div>
						<p className="text-xs text-muted-foreground">+8% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pendentes</CardTitle>
						<ClockIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">12</div>
						<p className="text-xs text-muted-foreground">-3 vs ontem</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Taxa de Sucesso</CardTitle>
						<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">96.2%</div>
						<p className="text-xs text-muted-foreground">+1.2% vs mês anterior</p>
					</CardContent>
				</Card>
			</div>

			<div className="grid gap-4">
				{withdrawals.map((withdrawal) => (
					<Card key={withdrawal.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
										<BuildingIcon className="h-5 w-5 text-muted-foreground" />
									</div>
									<div>
										<CardTitle className="text-lg">
											{withdrawal.organizationName}
										</CardTitle>
										<CardDescription className="flex items-center gap-2">
											<CalendarIcon className="h-4 w-4" />
											{new Date(withdrawal.createdAt).toLocaleDateString("pt-BR")} às{" "}
											{new Date(withdrawal.createdAt).toLocaleTimeString("pt-BR", {
												hour: "2-digit",
												minute: "2-digit"
											})}
										</CardDescription>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<div className="text-right">
										<div className="text-lg font-bold">
											R$ {(withdrawal.amountCents / 100).toLocaleString("pt-BR", {
												minimumFractionDigits: 2
											})}
										</div>
										<div className="text-sm text-muted-foreground">
											{withdrawal.bankAccount}
										</div>
									</div>
									{getStatusBadge(withdrawal.status)}
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="outline" size="sm">
												<MoreHorizontalIcon className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem>Visualizar</DropdownMenuItem>
											<DropdownMenuItem>Processar</DropdownMenuItem>
											<DropdownMenuItem>Cancelar</DropdownMenuItem>
											<DropdownMenuItem className="text-destructive">
												Rejeitar
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-4">
								<div className="flex items-center space-x-2">
									<UserIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Solicitado por</p>
										<p className="text-xs text-muted-foreground">
											{withdrawal.requestedBy}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<BuildingIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">ID do Saque</p>
										<p className="text-xs text-muted-foreground font-mono">
											{withdrawal.id}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Descrição</p>
										<p className="text-xs text-muted-foreground">
											{withdrawal.description}
										</p>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
