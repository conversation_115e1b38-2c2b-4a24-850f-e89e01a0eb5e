import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Switch } from "@ui/components/switch";
import { Label } from "@ui/components/label";
import {
	SearchIcon,
	FilterIcon,
	PlusIcon,
	MoreHorizontalIcon,
	WebhookIcon,
	CalendarIcon,
	DollarSignIcon,
	CheckCircleIcon,
	XCircleIcon,
	ClockIcon,
	AlertCircleIcon,
	SettingsIcon,
	ActivityIcon,
	CreditCardIcon,
	SmartphoneIcon,
	ReceiptIcon
} from "lucide-react";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";

export default async function AdminGatewaysPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	// Simulando dados dos gateways de pagamento
	const gateways = [
		{
			id: "stripe",
			name: "Stripe",
			description: "Gateway de pagamento internacional",
			status: "ACTIVE",
			icon: "💳",
			transactionCount: 1234,
			totalVolume: 12500000, // R$ 125.000,00
			fee: 2.9,
			createdAt: new Date("2024-01-01T00:00:00Z"),
			lastActivity: new Date("2024-01-15T10:30:00Z"),
			features: ["Cartão de Crédito", "PIX", "Boleto", "Parcelamento"]
		},
		{
			id: "mercadopago",
			name: "Mercado Pago",
			description: "Gateway brasileiro com PIX e boleto",
			status: "ACTIVE",
			icon: "🟢",
			transactionCount: 856,
			totalVolume: 8900000, // R$ 89.000,00
			fee: 3.5,
			createdAt: new Date("2024-01-05T00:00:00Z"),
			lastActivity: new Date("2024-01-15T09:15:00Z"),
			features: ["PIX", "Boleto", "Cartão de Crédito", "Pix Parcelado"]
		},
		{
			id: "pagseguro",
			name: "PagSeguro",
			description: "Gateway brasileiro da UOL",
			status: "INACTIVE",
			icon: "🟡",
			transactionCount: 234,
			totalVolume: 3400000, // R$ 34.000,00
			fee: 4.0,
			createdAt: new Date("2024-01-10T00:00:00Z"),
			lastActivity: new Date("2024-01-12T14:20:00Z"),
			features: ["Cartão de Crédito", "Boleto", "Débito Online"]
		},
		{
			id: "asaas",
			name: "Asaas",
			description: "Gateway brasileiro especializado em PIX",
			status: "ACTIVE",
			icon: "🔵",
			transactionCount: 567,
			totalVolume: 6700000, // R$ 67.000,00
			fee: 1.9,
			createdAt: new Date("2024-01-08T00:00:00Z"),
			lastActivity: new Date("2024-01-15T11:45:00Z"),
			features: ["PIX", "Boleto", "Cartão de Crédito", "Cobrança Recorrente"]
		}
	];

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "ACTIVE":
				return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200"><CheckCircleIcon className="w-3 h-3 mr-1" />Ativo</Badge>;
			case "INACTIVE":
				return <Badge variant="secondary" className="bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200"><XCircleIcon className="w-3 h-3 mr-1" />Inativo</Badge>;
			case "MAINTENANCE":
				return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200"><ClockIcon className="w-3 h-3 mr-1" />Manutenção</Badge>;
			case "ERROR":
				return <Badge variant="destructive"><AlertCircleIcon className="w-3 h-3 mr-1" />Erro</Badge>;
			default:
				return <Badge variant="outline">{status}</Badge>;
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Gateways de Pagamento</h1>
					<p className="text-muted-foreground">
						Configure e gerencie os gateways de pagamento disponíveis. Total: {gateways.length}
					</p>
				</div>
				<Button>
					<PlusIcon className="mr-2 h-4 w-4" />
					Adicionar Gateway
				</Button>
			</div>

			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar gateways..."
						className="pl-10"
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Volume Total</CardTitle>
						<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">R$ 315.000,00</div>
						<p className="text-xs text-muted-foreground">+18% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Transações</CardTitle>
						<ActivityIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">2.891</div>
						<p className="text-xs text-muted-foreground">+12% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Gateways Ativos</CardTitle>
						<CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">3</div>
						<p className="text-xs text-muted-foreground">de 4 gateways</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Taxa Média</CardTitle>
						<CreditCardIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">3.1%</div>
						<p className="text-xs text-muted-foreground">-0.2% vs mês anterior</p>
					</CardContent>
				</Card>
			</div>

			<div className="grid gap-4">
				{gateways.map((gateway) => (
					<Card key={gateway.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<div className="text-3xl">
										{gateway.icon}
									</div>
									<div>
										<CardTitle className="text-lg">
											{gateway.name}
										</CardTitle>
										<CardDescription>
											{gateway.description}
										</CardDescription>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<div className="text-right">
										<div className="text-lg font-bold">
											{gateway.transactionCount.toLocaleString()} transações
										</div>
										<div className="text-sm text-muted-foreground">
											R$ {(gateway.totalVolume / 100).toLocaleString("pt-BR", {
												minimumFractionDigits: 2
											})} processados
										</div>
									</div>
									{getStatusBadge(gateway.status)}
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="outline" size="sm">
												<MoreHorizontalIcon className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem>Configurar</DropdownMenuItem>
											<DropdownMenuItem>Testar</DropdownMenuItem>
											<DropdownMenuItem>Logs</DropdownMenuItem>
											<DropdownMenuItem className="text-destructive">
												Desativar
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-4 gap-4">
								<div className="flex items-center space-x-2">
									<CreditCardIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Taxa</p>
										<p className="text-xs text-muted-foreground">
											{gateway.fee}% por transação
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<CalendarIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Última Atividade</p>
										<p className="text-xs text-muted-foreground">
											{new Date(gateway.lastActivity).toLocaleDateString("pt-BR")}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<SettingsIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Status</p>
										<div className="flex items-center gap-2">
											<Switch
												checked={gateway.status === "ACTIVE"}
												className="scale-75"
											/>
											<Label className="text-xs">
												{gateway.status === "ACTIVE" ? "Ativo" : "Inativo"}
											</Label>
										</div>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<WebhookIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Recursos</p>
										<p className="text-xs text-muted-foreground">
											{gateway.features.length} disponíveis
										</p>
									</div>
								</div>
							</div>

							{/* Features */}
							<div className="mt-4 pt-4 border-t">
								<p className="text-sm font-medium mb-2">Recursos Disponíveis:</p>
								<div className="flex flex-wrap gap-2">
									{gateway.features.map((feature, index) => (
										<Badge key={index} variant="outline" className="text-xs">
											{feature}
										</Badge>
									))}
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
