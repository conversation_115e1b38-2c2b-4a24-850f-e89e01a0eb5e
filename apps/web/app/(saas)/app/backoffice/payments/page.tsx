import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import {
	SearchIcon,
	FilterIcon,
	PlusIcon,
	MoreHorizontalIcon,
	CreditCardIcon,
	CalendarIcon,
	DollarSignIcon,
	CheckCircleIcon,
	XCircleIcon,
	ClockIcon,
	AlertCircleIcon
} from "lucide-react";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";
import { db } from "@repo/database/prisma/client";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";

interface SearchParams {
	query?: string;
	status?: string;
	method?: string;
}

export default async function AdminPaymentsPage({
	searchParams,
}: {
	searchParams: Promise<SearchParams>;
}) {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	const { query, status, method } = await searchParams;

	// Buscar transações de pagamento
	const payments = await db.transaction.findMany({
		where: {
			...(query && {
				OR: [
					{ id: { contains: query } },
					{ organization: { name: { contains: query } } },
					{ user: { name: { contains: query } } },
				],
			}),
			...(status && { status: status as any }),
			...(method && { paymentMethod: method as any }),
		},
		include: {
			organization: true,
			user: true,
		},
		orderBy: { createdAt: "desc" },
		take: 50,
	});

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "COMPLETED":
				return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200"><CheckCircleIcon className="w-3 h-3 mr-1" />Pago</Badge>;
			case "PENDING":
				return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200"><ClockIcon className="w-3 h-3 mr-1" />Pendente</Badge>;
			case "FAILED":
				return <Badge variant="destructive"><XCircleIcon className="w-3 h-3 mr-1" />Falhou</Badge>;
			case "CANCELLED":
				return <Badge variant="outline"><AlertCircleIcon className="w-3 h-3 mr-1" />Cancelado</Badge>;
			default:
				return <Badge variant="outline">{status}</Badge>;
		}
	};

	const getPaymentMethodIcon = (method: string) => {
		switch (method) {
			case "PIX":
				return "💳";
			case "CREDIT_CARD":
				return "💳";
			case "BOLETO":
				return "📄";
			default:
				return "💳";
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Pagamentos</h1>
					<p className="text-muted-foreground">
						Gerencie todos os pagamentos do sistema. Total: {payments.length}
					</p>
				</div>
				<Button>
					<PlusIcon className="mr-2 h-4 w-4" />
					Novo Pagamento
				</Button>
			</div>

			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar pagamentos..."
						className="pl-10"
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Pago</CardTitle>
						<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">R$ 45.230,00</div>
						<p className="text-xs text-muted-foreground">+12% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pagamentos</CardTitle>
						<CreditCardIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">1.234</div>
						<p className="text-xs text-muted-foreground">+8% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Taxa de Sucesso</CardTitle>
						<CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">98.5%</div>
						<p className="text-xs text-muted-foreground">+0.2% vs mês anterior</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pendentes</CardTitle>
						<ClockIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">23</div>
						<p className="text-xs text-muted-foreground">-5 vs ontem</p>
					</CardContent>
				</Card>
			</div>

			<div className="grid gap-4">
				{payments.map((payment) => (
					<Card key={payment.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<div className="text-2xl">
										{getPaymentMethodIcon(payment.paymentMethod || "CREDIT_CARD")}
									</div>
									<div>
										<CardTitle className="text-lg">
											{payment.organization?.name || "Organização não encontrada"}
										</CardTitle>
										<CardDescription className="flex items-center gap-2">
											<CalendarIcon className="h-4 w-4" />
											{new Date(payment.createdAt).toLocaleDateString("pt-BR")} às{" "}
											{new Date(payment.createdAt).toLocaleTimeString("pt-BR", {
												hour: "2-digit",
												minute: "2-digit"
											})}
										</CardDescription>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<div className="text-right">
										<div className="text-lg font-bold">
											R$ {((payment.amountCents || 0) / 100).toLocaleString("pt-BR", {
												minimumFractionDigits: 2
											})}
										</div>
										<div className="text-sm text-muted-foreground">
											{payment.paymentMethod || "N/A"}
										</div>
									</div>
									{getStatusBadge(payment.status)}
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="outline" size="sm">
												<MoreHorizontalIcon className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem>Visualizar</DropdownMenuItem>
											<DropdownMenuItem>Reenviar</DropdownMenuItem>
											<DropdownMenuItem>Cancelar</DropdownMenuItem>
											<DropdownMenuItem className="text-destructive">
												Estornar
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-4">
								<div className="flex items-center space-x-2">
									<CreditCardIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">ID da Transação</p>
										<p className="text-xs text-muted-foreground font-mono">
											{payment.id.slice(0, 8)}...
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<CalendarIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Cliente</p>
										<p className="text-xs text-muted-foreground">
											{payment.user?.name || "Usuário não encontrado"}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Taxa</p>
										<p className="text-xs text-muted-foreground">
											R$ {((payment.feeCents || 0) / 100).toLocaleString("pt-BR", {
												minimumFractionDigits: 2
											})}
										</p>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
