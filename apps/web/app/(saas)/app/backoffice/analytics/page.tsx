import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
	BarChart3Icon,
	CalendarIcon,
	DownloadIcon,
	TrendingUpIcon,
	TrendingDownIcon,
	DollarSignIcon,
	UsersIcon,
	BuildingIcon,
	CreditCardIcon,
	ActivityIcon,
	ArrowUpIcon,
	ArrowDownIcon
} from "lucide-react";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";

export default async function AdminAnalyticsPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	// Simulando dados de analytics
	const metrics = {
		revenue: {
			current: 125000, // R$ 125.000,00
			previous: 110000, // R$ 110.000,00
			change: 13.6
		},
		transactions: {
			current: 2341,
			previous: 1987,
			change: 17.8
		},
		organizations: {
			current: 45,
			previous: 38,
			change: 18.4
		},
		users: {
			current: 234,
			previous: 198,
			change: 18.2
		}
	};

	const chartData = [
		{ month: "Jan", revenue: 45000, transactions: 890 },
		{ month: "Fev", revenue: 52000, transactions: 1023 },
		{ month: "Mar", revenue: 48000, transactions: 945 },
		{ month: "Abr", revenue: 61000, transactions: 1187 },
		{ month: "Mai", revenue: 58000, transactions: 1123 },
		{ month: "Jun", revenue: 67000, transactions: 1298 },
		{ month: "Jul", revenue: 72000, transactions: 1456 },
		{ month: "Ago", revenue: 68000, transactions: 1389 },
		{ month: "Set", revenue: 75000, transactions: 1523 },
		{ month: "Out", revenue: 82000, transactions: 1678 },
		{ month: "Nov", revenue: 91000, transactions: 1823 },
		{ month: "Dez", revenue: 125000, transactions: 2341 }
	];

	const topOrganizations = [
		{ name: "Academia Digital", revenue: 25000, transactions: 456, growth: 12.5 },
		{ name: "Tech Solutions", revenue: 18000, transactions: 334, growth: 8.3 },
		{ name: "Digital Agency", revenue: 15000, transactions: 289, growth: 15.7 },
		{ name: "E-commerce Plus", revenue: 12000, transactions: 234, growth: 22.1 },
		{ name: "Consultoria Pro", revenue: 9500, transactions: 187, growth: 5.2 }
	];

	const paymentMethods = [
		{ method: "PIX", count: 1234, percentage: 52.7, revenue: 65000 },
		{ method: "Cartão de Crédito", count: 789, percentage: 33.7, revenue: 42000 },
		{ method: "Boleto", count: 234, percentage: 10.0, revenue: 12000 },
		{ method: "Débito Online", count: 84, percentage: 3.6, revenue: 6000 }
	];

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Analytics</h1>
					<p className="text-muted-foreground">
						Análise detalhada do desempenho da plataforma
					</p>
				</div>
				<div className="flex items-center gap-2">
					<Select defaultValue="30">
						<SelectTrigger className="w-32">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="7">7 dias</SelectItem>
							<SelectItem value="30">30 dias</SelectItem>
							<SelectItem value="90">90 dias</SelectItem>
							<SelectItem value="365">1 ano</SelectItem>
						</SelectContent>
					</Select>
					<Button variant="outline">
						<DownloadIcon className="mr-2 h-4 w-4" />
						Exportar
					</Button>
				</div>
			</div>

			{/* Métricas Principais */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Receita Total</CardTitle>
						<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							R$ {metrics.revenue.current.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
						</div>
						<div className="flex items-center text-xs text-muted-foreground">
							{metrics.revenue.change > 0 ? (
								<ArrowUpIcon className="h-3 w-3 text-green-500 mr-1" />
							) : (
								<ArrowDownIcon className="h-3 w-3 text-red-500 mr-1" />
							)}
							<span className={metrics.revenue.change > 0 ? "text-green-500" : "text-red-500"}>
								{Math.abs(metrics.revenue.change)}%
							</span>
							<span className="ml-1">vs mês anterior</span>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Transações</CardTitle>
						<CreditCardIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{metrics.transactions.current.toLocaleString()}
						</div>
						<div className="flex items-center text-xs text-muted-foreground">
							<ArrowUpIcon className="h-3 w-3 text-green-500 mr-1" />
							<span className="text-green-500">
								{metrics.transactions.change}%
							</span>
							<span className="ml-1">vs mês anterior</span>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Organizações</CardTitle>
						<BuildingIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{metrics.organizations.current}</div>
						<div className="flex items-center text-xs text-muted-foreground">
							<ArrowUpIcon className="h-3 w-3 text-green-500 mr-1" />
							<span className="text-green-500">
								{metrics.organizations.change}%
							</span>
							<span className="ml-1">vs mês anterior</span>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Usuários</CardTitle>
						<UsersIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{metrics.users.current}</div>
						<div className="flex items-center text-xs text-muted-foreground">
							<ArrowUpIcon className="h-3 w-3 text-green-500 mr-1" />
							<span className="text-green-500">
								{metrics.users.change}%
							</span>
							<span className="ml-1">vs mês anterior</span>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Gráficos e Tabelas */}
			<div className="grid gap-6 md:grid-cols-2">
				{/* Receita Mensal */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<TrendingUpIcon className="h-5 w-5" />
							Receita Mensal
						</CardTitle>
						<CardDescription>
							Evolução da receita nos últimos 12 meses
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{chartData.slice(-6).map((data, index) => (
								<div key={data.month} className="flex items-center justify-between">
									<div className="flex items-center gap-2">
										<div className="w-2 h-2 bg-primary rounded-full" />
										<span className="text-sm font-medium">{data.month}</span>
									</div>
									<div className="text-right">
										<div className="text-sm font-bold">
											R$ {data.revenue.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
										</div>
										<div className="text-xs text-muted-foreground">
											{data.transactions} transações
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				{/* Métodos de Pagamento */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<CreditCardIcon className="h-5 w-5" />
							Métodos de Pagamento
						</CardTitle>
						<CardDescription>
							Distribuição por método de pagamento
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{paymentMethods.map((method, index) => (
								<div key={method.method} className="space-y-2">
									<div className="flex items-center justify-between">
										<span className="text-sm font-medium">{method.method}</span>
										<span className="text-sm text-muted-foreground">
											{method.percentage}%
										</span>
									</div>
									<div className="w-full bg-muted rounded-full h-2">
										<div
											className="bg-primary h-2 rounded-full transition-all duration-300"
											style={{ width: `${method.percentage}%` }}
										/>
									</div>
									<div className="flex justify-between text-xs text-muted-foreground">
										<span>{method.count} transações</span>
										<span>R$ {method.revenue.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}</span>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Top Organizações */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<BuildingIcon className="h-5 w-5" />
						Top Organizações
					</CardTitle>
					<CardDescription>
						Organizações com maior volume de transações
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{topOrganizations.map((org, index) => (
							<div key={org.name} className="flex items-center justify-between p-4 border rounded-lg">
								<div className="flex items-center gap-4">
									<div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm font-bold">
										{index + 1}
									</div>
									<div>
										<div className="font-medium">{org.name}</div>
										<div className="text-sm text-muted-foreground">
											{org.transactions} transações
										</div>
									</div>
								</div>
								<div className="text-right">
									<div className="font-bold">
										R$ {org.revenue.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
									</div>
									<div className="flex items-center text-xs text-green-500">
										<TrendingUpIcon className="h-3 w-3 mr-1" />
										+{org.growth}%
									</div>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
