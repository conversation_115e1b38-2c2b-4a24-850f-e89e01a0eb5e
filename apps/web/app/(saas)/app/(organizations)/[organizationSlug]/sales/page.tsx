import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import ClientPage from "./ClientPage";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Vendas", // " | [App Name] is added by the template"
  };
}

export default async function SalesPage({
  params,
  searchParams,
}: {
  params: Promise<{ organizationSlug: string }>;
  searchParams: Promise<{
    page?: string;
    limit?: string;
    sort?: string;
    order?: string;
    search?: string;
    product_id?: string[] | string;
    status?: string[] | string;
    payment_method?: string[] | string;
  }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  const resolvedSearchParams = await searchParams;

  // Parse pagination parameters
  const page = parseInt(resolvedSearchParams.page || "1", 10);
  const limit = parseInt(resolvedSearchParams.limit || "50", 10);

  // Parse sorting parameters
  const sort = resolvedSearchParams.sort || "createdAt";
  const order = resolvedSearchParams.order || "desc";

  // Parse filter parameters
  const productIds = resolvedSearchParams.product_id
    ? Array.isArray(resolvedSearchParams.product_id)
      ? resolvedSearchParams.product_id
      : [resolvedSearchParams.product_id]
    : undefined;

  const statuses = resolvedSearchParams.status
    ? Array.isArray(resolvedSearchParams.status)
      ? resolvedSearchParams.status
      : [resolvedSearchParams.status]
    : undefined;

  const paymentMethods = resolvedSearchParams.payment_method
    ? Array.isArray(resolvedSearchParams.payment_method)
      ? resolvedSearchParams.payment_method
      : [resolvedSearchParams.payment_method]
    : undefined;

  const searchTerm = resolvedSearchParams.search;

  return (
    <ClientPage
      organization={organization}
      initialFilters={{
        page,
        limit,
        sort,
        order,
        search: searchTerm,
        productIds,
        statuses,
        paymentMethods,
      }}
    />
  );
}
