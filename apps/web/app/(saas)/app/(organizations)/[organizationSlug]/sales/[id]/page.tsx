import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import ClientPage from "./ClientPage";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ organizationSlug: string; id: string }>;
}): Promise<Metadata> {
  const { id } = await params;
  return {
    title: `Venda ${id}`,
  };
}

export default async function SaleDetailPage({
  params,
}: {
  params: Promise<{ organizationSlug: string; id: string }>;
}) {
  const { organizationSlug, id } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <ClientPage
      organization={organization}
      saleId={id}
    />
  );
}
