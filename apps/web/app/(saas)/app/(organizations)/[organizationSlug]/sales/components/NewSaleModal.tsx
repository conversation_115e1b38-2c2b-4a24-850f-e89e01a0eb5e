"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { RadioGroup, RadioGroupItem } from "@ui/components/radio-group";
import { Label } from "@ui/components/label";
import { Separator } from "@ui/components/separator";
import { Badge } from "@ui/components/badge";
import { LoaderIcon, CreditCard, Smartphone, FileText, Copy, ExternalLink } from "lucide-react";
import { useProducts } from "@saas/products/hooks/useProductsApi";
import { formatCurrency, formatCurrencyFromCents } from "@lib/utils";
import { toast } from "sonner";

// Schema de validação
const newSaleSchema = z.object({
  productId: z.string().min(1, "Selecione um produto"),
  customerName: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  customerEmail: z.string().email("Email inválido"),
  customerPhone: z.string().min(10, "Telefone deve ter pelo menos 10 dígitos"),
  customerDocument: z.string().optional(),
  paymentMethod: z.enum(["pix", "card", "boleto"], {
    required_error: "Selecione um método de pagamento",
  }),
  amount: z.number().min(1, "Valor deve ser maior que R$ 0,01"),
  notes: z.string().optional(),
});

type NewSaleFormData = z.infer<typeof newSaleSchema>;

interface NewSaleModalProps {
  organizationId: string;
  organizationSlug: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function NewSaleModal({
  organizationId,
  organizationSlug,
  open,
  onOpenChange,
  onSuccess,
}: NewSaleModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<"form" | "payment" | "success">("form");
  const [paymentLink, setPaymentLink] = useState<string>("");

  // Buscar produtos da organização
  const { data: productsData, isLoading: productsLoading } = useProducts(organizationId, {
    status: "PUBLISHED",
    limit: 100,
  });

  const products = productsData?.products || [];

  const form = useForm<NewSaleFormData>({
    resolver: zodResolver(newSaleSchema),
    defaultValues: {
      productId: "",
      customerName: "",
      customerEmail: "",
      customerPhone: "",
      customerDocument: "",
      paymentMethod: "pix",
      amount: 0,
      notes: "",
    },
  });

  const selectedProductId = form.watch("productId");
  const selectedProduct = products.find(p => p.id === selectedProductId);
  const paymentMethod = form.watch("paymentMethod");

  // Atualizar preço quando produto for selecionado
  const handleProductChange = (productId: string) => {
    const product = products.find(p => p.id === productId);
    if (product) {
      form.setValue("amount", product.priceCents / 100);
    }
  };

  const onSubmit = async (data: NewSaleFormData) => {
    try {
      setIsLoading(true);

      // Simular criação da venda
      const saleData = {
        organizationId,
        productId: data.productId,
        customer: {
          name: data.customerName,
          email: data.customerEmail,
          phone: data.customerPhone,
          document: data.customerDocument,
        },
        amount: data.amount,
        paymentMethod: data.paymentMethod,
        notes: data.notes,
      };

      // TODO: Implementar chamada real para API
      console.log("Creating sale:", saleData);

      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simular geração de link de pagamento
      const mockPaymentLink = `https://pay.supgateway.com/checkout/${Math.random().toString(36).substr(2, 9)}`;
      setPaymentLink(mockPaymentLink);

      setStep("payment");
      toast.success("Venda criada com sucesso!");

    } catch (error) {
      console.error("Error creating sale:", error);
      toast.error("Erro ao criar venda. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(paymentLink);
    toast.success("Link copiado para a área de transferência!");
  };

  const handleSendLink = () => {
    const customerPhone = form.getValues("customerPhone");
    const customerName = form.getValues("customerName");
    const productName = selectedProduct?.name;
    
    const message = `Olá ${customerName}! Aqui está o link para pagamento do ${productName}: ${paymentLink}`;
    const whatsappUrl = `https://wa.me/55${customerPhone.replace(/\D/g, "")}?text=${encodeURIComponent(message)}`;
    
    window.open(whatsappUrl, "_blank");
    toast.success("Redirecionando para WhatsApp...");
  };

  const handleFinish = () => {
    setStep("form");
    form.reset();
    setPaymentLink("");
    onSuccess();
  };

  const handleClose = () => {
    if (step === "payment") {
      // Se estiver na tela de pagamento, perguntar se quer realmente fechar
      if (confirm("Tem certeza que deseja fechar? O link de pagamento foi gerado.")) {
        handleFinish();
      }
    } else {
      handleFinish();
    }
  };

  const paymentMethodConfig = {
    pix: {
      label: "PIX",
      icon: Smartphone,
      color: "text-green-600",
      description: "Pagamento instantâneo",
    },
    card: {
      label: "Cartão de Crédito",
      icon: CreditCard,
      color: "text-blue-600",
      description: "Até 12x sem juros",
    },
    boleto: {
      label: "Boleto Bancário",
      icon: FileText,
      color: "text-orange-600",
      description: "Vencimento em 3 dias",
    },
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {step === "form" && "Nova Venda"}
            {step === "payment" && "Link de Pagamento Gerado"}
            {step === "success" && "Venda Criada com Sucesso"}
          </DialogTitle>
          <DialogDescription>
            {step === "form" && "Crie uma venda manual para um cliente"}
            {step === "payment" && "Compartilhe o link de pagamento com o cliente"}
            {step === "success" && "A venda foi criada e o cliente pode efetuar o pagamento"}
          </DialogDescription>
        </DialogHeader>

        {step === "form" && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Seleção do Produto */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Produto</h3>
                <FormField
                  control={form.control}
                  name="productId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Produto *</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          handleProductChange(value);
                        }}
                        value={field.value}
                        disabled={productsLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione um produto" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {products.map((product) => (
                            <SelectItem key={product.id} value={product.id}>
                              <div className="flex items-center justify-between w-full">
                                <span>{product.name}</span>
                                <Badge variant="secondary" className="ml-2">
                                  {formatCurrencyFromCents(product.priceCents)}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {selectedProduct && (
                  <div className="p-4 bg-muted rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{selectedProduct.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {selectedProduct.description}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-green-600">
                          {formatCurrencyFromCents(selectedProduct.priceCents)}
                        </div>
                        <Badge variant="outline">{selectedProduct.type}</Badge>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              {/* Dados do Cliente */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Dados do Cliente</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="customerName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome Completo *</FormLabel>
                        <FormControl>
                          <Input placeholder="João Silva" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="customerEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email *</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" type="email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="customerPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Telefone/WhatsApp *</FormLabel>
                        <FormControl>
                          <Input placeholder="(11) 99999-9999" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="customerDocument"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CPF/CNPJ</FormLabel>
                        <FormControl>
                          <Input placeholder="000.000.000-00" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <Separator />

              {/* Método de Pagamento */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Método de Pagamento</h3>
                <FormField
                  control={form.control}
                  name="paymentMethod"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className="grid grid-cols-1 md:grid-cols-3 gap-4"
                        >
                          {Object.entries(paymentMethodConfig).map(([key, config]) => {
                            const Icon = config.icon;
                            return (
                              <div key={key} className="flex items-center space-x-2">
                                <RadioGroupItem value={key} id={key} />
                                <Label
                                  htmlFor={key}
                                  className="flex items-center space-x-3 cursor-pointer flex-1 p-3 border rounded-lg hover:bg-muted"
                                >
                                  <Icon className={`h-5 w-5 ${config.color}`} />
                                  <div>
                                    <div className="font-medium">{config.label}</div>
                                    <div className="text-sm text-muted-foreground">
                                      {config.description}
                                    </div>
                                  </div>
                                </Label>
                              </div>
                            );
                          })}
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* Valor e Observações */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Valor (R$) *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0.01"
                            placeholder="0,00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex items-end">
                    <div className="text-right">
                      <Label className="text-sm text-muted-foreground">Total</Label>
                      <div className="text-2xl font-bold text-green-600">
                        {formatCurrency(form.watch("amount") || 0)}
                      </div>
                    </div>
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Observações</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Observações internas sobre a venda..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isLoading}
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />}
                  Criar Venda
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}

        {step === "payment" && (
          <div className="space-y-6">
            {/* Resumo da Venda */}
            <div className="p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Resumo da Venda</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Cliente:</span>
                  <span className="font-medium">{form.getValues("customerName")}</span>
                </div>
                <div className="flex justify-between">
                  <span>Produto:</span>
                  <span className="font-medium">{selectedProduct?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Método:</span>
                  <Badge variant="outline">
                    {paymentMethodConfig[paymentMethod].label}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Valor:</span>
                  <span className="font-bold text-green-600">
                    {formatCurrency(form.getValues("amount"))}
                  </span>
                </div>
              </div>
            </div>

            {/* Link de Pagamento */}
            <div className="space-y-4">
              <h3 className="font-semibold">Link de Pagamento</h3>
              <div className="flex items-center space-x-2 p-3 bg-muted rounded-lg">
                <Input
                  value={paymentLink}
                  readOnly
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleCopyLink}
                >
                  <Copy className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(paymentLink, "_blank")}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Ações */}
            <div className="space-y-3">
              <Button
                onClick={handleSendLink}
                className="w-full"
                size="lg"
              >
                <Smartphone className="h-4 w-4 mr-2" />
                Enviar via WhatsApp
              </Button>
              
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  onClick={handleCopyLink}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copiar Link
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.open(paymentLink, "_blank")}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Abrir Link
                </Button>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setStep("form")}
              >
                Nova Venda
              </Button>
              <Button onClick={handleFinish}>
                Finalizar
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
