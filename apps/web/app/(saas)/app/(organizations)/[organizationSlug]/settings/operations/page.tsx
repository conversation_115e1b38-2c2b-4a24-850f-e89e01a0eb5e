import { getActiveOrganization } from "@saas/auth/lib/server";
import { SettingsList } from "@saas/shared/components/SettingsList";
import { notFound } from "next/navigation";
import { OperationsSettings } from "@saas/organizations/components/OperationsSettings";

export default async function OperationsSettingsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <SettingsList>
      <OperationsSettings organizationId={organization.id} />
    </SettingsList>
  );
}
