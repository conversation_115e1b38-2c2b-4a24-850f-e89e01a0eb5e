"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Badge } from "@ui/components/badge";
import { Separator } from "@ui/components/separator";
import {
  PackageIcon,
  TrashIcon,
  CheckIcon,
  HelpCircleIcon,
  SmartphoneIcon,
  UsersIcon,
  DollarSignIcon
} from "lucide-react";

interface ProductConfigurationClientProps {
  product: any; // Substitua por um tipo mais específico
  organizationSlug: string;
}

export function ProductConfigurationClient({
  product,
  organizationSlug
}: ProductConfigurationClientProps) {
  const [formData, setFormData] = useState({
    name: product.name || "",
    description: product.description || "",
    sellerName: "Ismael",
    sellerEmail: "<EMAIL>",
    sellerPhone: "",
    category: product.category?.name || "Tecnologia da Informação",
    format: product.type || "Serviço",
    language: "Português",
    currency: "Real (R$)",
    salesPage: "https://app.kirvano.com/products",
    isActive: product.status === "PUBLISHED",
  });

  const [hasChanges, setHasChanges] = useState(false);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(cents / 100);
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'COURSE': return 'Curso';
      case 'EBOOK': return 'E-book';
      case 'MENTORSHIP': return 'Mentoria';
      case 'SUBSCRIPTION': return 'Assinatura';
      case 'BUNDLE': return 'Pacote';
      case 'SERVICE': return 'Serviço';
      default: return type;
    }
  };

  return (
    <div className="p-6 space-y-8">
      {/* Product Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <PackageIcon className="h-8 w-8 text-white" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                {getTypeLabel(product.type)}
              </Badge>
            </div>
            <h1 className="text-2xl font-bold text-foreground mt-1">{product.name}</h1>
          </div>
        </div>

        <div className="text-right">
          <div className="text-sm font-medium text-foreground">R$ 0,00 faturado</div>
          <div className="text-xs text-muted-foreground">0 venda realizada</div>
        </div>
      </div>

      <div className="space-y-8">
        {/* Informações Básicas */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Informações básicas</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Product Cover */}
            <div className="flex items-center gap-4">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <PackageIcon className="h-10 w-10 text-white" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Product cover</p>
                <Button variant="outline" size="sm" className="mt-2">
                  Alterar imagem
                </Button>
              </div>
            </div>

            {/* Nome */}
            <div className="space-y-2">
              <Label htmlFor="name">Nome</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nome do produto"
              />
              <p className="text-xs text-muted-foreground">
                Esse nome será exibido em todos os locais da Kirvano - ({formData.name.length}/60 caracteres)
              </p>
            </div>

            {/* Descrição */}
            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Descrição do produto"
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                {formData.description.length}/200 caracteres
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Suporte ao Comprador */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Suporte ao comprador</CardTitle>
            <CardDescription className="text-sm text-amber-600 dark:text-amber-400">
              (Obrigatório)
            </CardDescription>
            <p className="text-sm text-muted-foreground">
              Informações que serão apresentadas ao seu cliente.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="sellerName">Nome do vendedor</Label>
              <Input
                id="sellerName"
                value={formData.sellerName}
                onChange={(e) => handleInputChange('sellerName', e.target.value)}
                placeholder="Nome do vendedor"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sellerEmail">E-mail</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="sellerEmail"
                  value={formData.sellerEmail}
                  onChange={(e) => handleInputChange('sellerEmail', e.target.value)}
                  placeholder="E-mail do vendedor"
                />
                <Button variant="outline" size="sm">
                  Atualizar
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Atual: {formData.sellerEmail}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sellerPhone">Telefone</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="sellerPhone"
                  value={formData.sellerPhone}
                  onChange={(e) => handleInputChange('sellerPhone', e.target.value)}
                  placeholder="Telefone do vendedor"
                />
                <Button variant="outline" size="sm">
                  Cadastrar
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                (opcional) Enviaremos um código de verificação para validação.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Preferências */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Preferências</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Categoria</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Tecnologia da Informação">Tecnologia da Informação</SelectItem>
                    <SelectItem value="Marketing">Marketing</SelectItem>
                    <SelectItem value="Design">Design</SelectItem>
                    <SelectItem value="Negócios">Negócios</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="format">Formato</Label>
                <Select value={formData.format} onValueChange={(value) => handleInputChange('format', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Serviço">Serviço</SelectItem>
                    <SelectItem value="Curso">Curso</SelectItem>
                    <SelectItem value="E-book">E-book</SelectItem>
                    <SelectItem value="Mentoria">Mentoria</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Idioma</Label>
                <Select value={formData.language} onValueChange={(value) => handleInputChange('language', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Português">Português</SelectItem>
                    <SelectItem value="English">English</SelectItem>
                    <SelectItem value="Español">Español</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">Moeda base</Label>
                <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Real (R$)">Real (R$)</SelectItem>
                    <SelectItem value="Dólar (US$)">Dólar (US$)</SelectItem>
                    <SelectItem value="Euro (€)">Euro (€)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="salesPage">Página de vendas</Label>
              <Input
                id="salesPage"
                value={formData.salesPage}
                onChange={(e) => handleInputChange('salesPage', e.target.value)}
                placeholder="https://app.kirvano.com/products"
              />
            </div>
          </CardContent>
        </Card>

        {/* Status */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Status</CardTitle>
            <CardDescription>
              Gerencie se o produto estará ou não ativo para vendas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="isActive" className="text-base font-medium">Ativo</Label>
                <p className="text-sm text-muted-foreground">
                  {formData.isActive ? "Produto ativo para vendas" : "Produto inativo"}
                </p>
              </div>
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleInputChange('isActive', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Recuperação Ativa */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              Recuperação ativa
              <HelpCircleIcon className="h-4 w-4 text-muted-foreground" />
            </CardTitle>
            <CardDescription>
              Com esse recurso reconquiste o cliente que está prestes a cancelar a compra ou recupere uma venda não finalizada.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline">
              Configurar
            </Button>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-6 border-t border-border/50">
          <Button variant="destructive" size="lg">
            <TrashIcon className="h-4 w-4 mr-2" />
            Excluir produto
          </Button>

          <Button
            size="lg"
            disabled={!hasChanges}
            className={hasChanges ? "bg-primary hover:bg-primary/90" : ""}
          >
            <CheckIcon className="h-4 w-4 mr-2" />
            Salvar alterações
          </Button>
        </div>
      </div>
    </div>
  );
}
