"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { 
  PlusIcon, 
  BarChart3Icon, 
  MoreHorizontalIcon,
  EditIcon,
  TrashIcon,
  EyeIcon,
  EyeOffIcon
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import { CreatePixelModal } from "./CreatePixelModal";

// Tipos temporários
interface Pixel {
  id: string;
  name: string;
  platform: "facebook" | "google" | "tiktok" | "custom";
  pixelId: string;
  events: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PixelsPageClientProps {
  organizationSlug: string;
  productId: string;
  product: any;
}

// Dados mockados
const mockPixels: Pixel[] = [
  {
    id: "1",
    name: "Facebook Pixel Principal",
    platform: "facebook",
    pixelId: "123456789012345",
    events: ["PageView", "Purchase", "AddToCart"],
    isActive: true,
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T15:30:00Z",
  },
  {
    id: "2",
    name: "Google Analytics",
    platform: "google",
    pixelId: "GA-XXXXXXXXX-X",
    events: ["page_view", "purchase"],
    isActive: true,
    createdAt: "2024-01-10T08:00:00Z",
    updatedAt: "2024-01-10T08:00:00Z",
  },
];

const platformLabels: Record<string, string> = {
  facebook: "Facebook",
  google: "Google",
  tiktok: "TikTok",
  custom: "Personalizado",
};

const platformColors: Record<string, string> = {
  facebook: "bg-blue-100 text-blue-800",
  google: "bg-green-100 text-green-800",
  tiktok: "bg-pink-100 text-pink-800",
  custom: "bg-gray-100 text-gray-800",
};

// Componente Badge simples
function Badge({ 
  children, 
  variant = "default", 
  className 
}: { 
  children: React.ReactNode; 
  variant?: "default" | "secondary" | "outline"; 
  className?: string;
}) {
  const baseClasses = "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium";
  const variantClasses = {
    default: "bg-primary text-primary-foreground",
    secondary: "bg-secondary text-secondary-foreground",
    outline: "border border-input bg-background text-foreground",
  };
  
  return (
    <span className={cn(baseClasses, variantClasses[variant], className)}>
      {children}
    </span>
  );
}

export function PixelsPageClient({
  organizationSlug,
  productId,
  product,
}: PixelsPageClientProps) {
  const [pixels, setPixels] = useState<Pixel[]>(mockPixels);

  const handleDeletePixel = (pixelId: string) => {
    // TODO: Implementar confirmação e chamada para API
    setPixels(prev => prev.filter(p => p.id !== pixelId));
  };

  const handleTogglePixel = (pixelId: string) => {
    setPixels(prev => prev.map(p => 
      p.id === pixelId ? { ...p, isActive: !p.isActive } : p
    ));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Produtos</span>
            <span>/</span>
            <span>{product.name.length > 20 ? `${product.name.substring(0, 20)}...` : product.name}</span>
            <span>/</span>
            <span className="text-foreground font-medium">Pixels de rastreamento</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm font-medium">Pessoa Física</div>
              <div className="text-xs text-muted-foreground">PF</div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium">R$ 0,00 faturado</div>
              <div className="text-xs text-muted-foreground">0 venda realizada</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Pixels de rastreamento</h1>
              <p className="text-muted-foreground">Configure pixels de rastreamento para analytics e remarketing</p>
            </div>
            <CreatePixelModal 
              organizationSlug={organizationSlug}
              productId={productId}
              product={product}
            />
          </div>

          {pixels.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Pixels Configurados</CardTitle>
                <CardDescription>
                  Gerencie seus pixels de rastreamento do Facebook, Google e outras plataformas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BarChart3Icon className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Nenhum pixel configurado</h3>
                  <p className="text-muted-foreground mb-4">
                    Configure pixels de rastreamento para acompanhar conversões e otimizar campanhas
                  </p>
                  <CreatePixelModal 
                    organizationSlug={organizationSlug}
                    productId={productId}
                    product={product}
                    variant="default"
                    buttonText="Configurar Primeiro Pixel"
                  />
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Pixels Configurados ({pixels.length})</CardTitle>
                <CardDescription>
                  Gerencie seus pixels de rastreamento do Facebook, Google e outras plataformas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Plataforma</TableHead>
                      <TableHead>ID do Pixel</TableHead>
                      <TableHead>Eventos</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Criado em</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pixels.map((pixel) => (
                      <TableRow key={pixel.id}>
                        <TableCell className="font-medium">
                          {pixel.name}
                        </TableCell>
                        <TableCell>
                          <Badge className={platformColors[pixel.platform]}>
                            {platformLabels[pixel.platform]}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <code className="text-sm bg-muted px-2 py-1 rounded">
                            {pixel.pixelId}
                          </code>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1 flex-wrap">
                            {pixel.events.slice(0, 2).map((event) => (
                              <Badge key={event} variant="secondary" className="text-xs">
                                {event}
                              </Badge>
                            ))}
                            {pixel.events.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{pixel.events.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={pixel.isActive ? "default" : "secondary"}>
                            {pixel.isActive ? "Ativo" : "Inativo"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDate(pixel.createdAt)}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontalIcon className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <EditIcon className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleTogglePixel(pixel.id)}>
                                {pixel.isActive ? (
                                  <>
                                    <EyeOffIcon className="h-4 w-4 mr-2" />
                                    Desativar
                                  </>
                                ) : (
                                  <>
                                    <EyeIcon className="h-4 w-4 mr-2" />
                                    Ativar
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                className="text-destructive"
                                onClick={() => handleDeletePixel(pixel.id)}
                              >
                                <TrashIcon className="h-4 w-4 mr-2" />
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
