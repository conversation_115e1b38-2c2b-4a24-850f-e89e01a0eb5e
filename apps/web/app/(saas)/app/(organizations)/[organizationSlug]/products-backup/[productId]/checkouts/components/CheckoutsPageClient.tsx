"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { 
  PlusIcon, 
  CreditCardIcon, 
  MoreHorizontalIcon,
  ExternalLinkIcon,
  EditIcon,
  TrashIcon,
  CopyIcon,
  EyeIcon
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import { CreateCheckoutModal } from "./CreateCheckoutModal";

// Tipos temporários - serão refinados quando integrarmos com a API
interface Checkout {
  id: string;
  name: string;
  layout: "default" | "minimal" | "modern";
  paymentMethods: string[];
  offersCount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  url?: string;
}

interface CheckoutsPageClientProps {
  organizationSlug: string;
  productId: string;
  product: any;
}

// Dados mockados para demonstração
const mockCheckouts: Checkout[] = [
  {
    id: "1",
    name: "Checkout Principal",
    layout: "default",
    paymentMethods: ["credit_card", "pix", "boleto"],
    offersCount: 3,
    isActive: true,
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T15:30:00Z",
    url: "https://checkout.exemplo.com/produto-1",
  },
  {
    id: "2", 
    name: "Checkout de Teste",
    layout: "minimal",
    paymentMethods: ["credit_card", "pix"],
    offersCount: 1,
    isActive: false,
    createdAt: "2024-01-10T08:00:00Z",
    updatedAt: "2024-01-10T08:00:00Z",
    url: "https://checkout.exemplo.com/produto-1-teste",
  },
];

const paymentMethodLabels: Record<string, string> = {
  credit_card: "Cartão",
  pix: "PIX",
  boleto: "Boleto",
};

const layoutLabels: Record<string, string> = {
  default: "Padrão",
  minimal: "Minimalista",
  modern: "Moderno",
};

// Componente Badge simples
function Badge({
  children,
  variant = "default",
  className
}: {
  children: React.ReactNode;
  variant?: "default" | "secondary" | "outline";
  className?: string;
}) {
  const baseClasses = "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium";
  const variantClasses = {
    default: "bg-primary text-primary-foreground",
    secondary: "bg-secondary text-secondary-foreground",
    outline: "border border-input bg-background text-foreground",
  };

  return (
    <span className={cn(baseClasses, variantClasses[variant], className)}>
      {children}
    </span>
  );
}

export function CheckoutsPageClient({
  organizationSlug,
  productId,
  product,
}: CheckoutsPageClientProps) {
  const [checkouts, setCheckouts] = useState<Checkout[]>(mockCheckouts);

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    // TODO: Mostrar toast de sucesso
  };

  const handleDeleteCheckout = (checkoutId: string) => {
    // TODO: Implementar confirmação e chamada para API
    setCheckouts(prev => prev.filter(c => c.id !== checkoutId));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Produtos</span>
            <span>/</span>
            <span>{product.name.length > 20 ? `${product.name.substring(0, 20)}...` : product.name}</span>
            <span>/</span>
            <span className="text-foreground font-medium">Checkouts</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm font-medium">Pessoa Física</div>
              <div className="text-xs text-muted-foreground">PF</div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium">R$ 0,00 faturado</div>
              <div className="text-xs text-muted-foreground">0 venda realizada</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Checkouts</h1>
              <p className="text-muted-foreground">Configure e gerencie seus checkouts de pagamento</p>
            </div>
            <CreateCheckoutModal 
              organizationSlug={organizationSlug}
              productId={productId}
              product={product}
            />
          </div>

          {checkouts.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Checkouts Configurados</CardTitle>
                <CardDescription>
                  Gerencie suas opções de pagamento e checkout
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CreditCardIcon className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Nenhum checkout configurado</h3>
                  <p className="text-muted-foreground mb-4">
                    Configure seu primeiro checkout para começar a receber pagamentos
                  </p>
                  <CreateCheckoutModal 
                    organizationSlug={organizationSlug}
                    productId={productId}
                    product={product}
                    variant="default"
                    buttonText="Configurar Checkout"
                  />
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Checkouts Configurados ({checkouts.length})</CardTitle>
                <CardDescription>
                  Gerencie suas opções de pagamento e checkout
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Layout</TableHead>
                      <TableHead>Métodos de Pagamento</TableHead>
                      <TableHead>Ofertas</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Criado em</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {checkouts.map((checkout) => (
                      <TableRow key={checkout.id}>
                        <TableCell className="font-medium">
                          {checkout.name}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {layoutLabels[checkout.layout]}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1 flex-wrap">
                            {checkout.paymentMethods.map((method) => (
                              <Badge key={method} variant="secondary" className="text-xs">
                                {paymentMethodLabels[method]}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {checkout.offersCount} ofertas
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge variant={checkout.isActive ? "default" : "secondary"}>
                            {checkout.isActive ? "Ativo" : "Inativo"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDate(checkout.createdAt)}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontalIcon className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <EyeIcon className="h-4 w-4 mr-2" />
                                Visualizar
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <EditIcon className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              {checkout.url && (
                                <>
                                  <DropdownMenuItem onClick={() => handleCopyUrl(checkout.url!)}>
                                    <CopyIcon className="h-4 w-4 mr-2" />
                                    Copiar URL
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <ExternalLinkIcon className="h-4 w-4 mr-2" />
                                    Abrir Checkout
                                  </DropdownMenuItem>
                                </>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                className="text-destructive"
                                onClick={() => handleDeleteCheckout(checkout.id)}
                              >
                                <TrashIcon className="h-4 w-4 mr-2" />
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
