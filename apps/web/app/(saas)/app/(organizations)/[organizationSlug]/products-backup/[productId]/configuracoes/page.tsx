import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { ProductConfigurationClient } from "./components/ProductConfigurationClient";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon, EyeIcon } from "lucide-react";
import Link from "next/link";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function ProductConfigurationPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
      _count: {
        select: {
          orders: true,
          enrollments: true,
        },
      },
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-6 py-4">
          {/* Breadcrumbs */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link
              href={`/app/${organizationSlug}/products`}
              className="hover:text-foreground transition-colors"
            >
              Produtos
            </Link>
            <span>/</span>
            <Link
              href={`/app/${organizationSlug}/products/${productId}`}
              className="hover:text-foreground transition-colors"
            >
              {product.name.length > 20 ? `${product.name.substring(0, 20)}...` : product.name}
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">Configurações</span>
          </div>

          {/* User Info */}
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm font-medium">Pessoa Física</div>
              <div className="text-xs text-muted-foreground">PF</div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium">R$ 0,00 faturado</div>
              <div className="text-xs text-muted-foreground">0 venda realizada</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <ProductConfigurationClient
          product={product}
          organizationSlug={organizationSlug}
        />
      </div>
    </div>
  );
}
