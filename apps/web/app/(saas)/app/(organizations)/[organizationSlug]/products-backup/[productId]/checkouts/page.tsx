import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { PlusIcon, CreditCardIcon } from "lucide-react";
import { CheckoutsPageClient } from "./components/CheckoutsPageClient";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function CheckoutsPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <CheckoutsPageClient
      organizationSlug={organizationSlug}
      productId={productId}
      product={product}
    />
  );
}
