import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database/prisma/client";
import { redirect } from "next/navigation";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon, EyeIcon } from "lucide-react";
import Link from "next/link";

interface EditProductPageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function EditProductPage({ params }: EditProductPageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={`Editar: ${product.name}`}
        subtitle="Modifique as informações do seu produto"
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={`/app/${organizationSlug}/products/${productId}`}>
                <EyeIcon className="h-4 w-4 mr-2" />
                Visualizar
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/app/${organizationSlug}/products`}>
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Voltar
              </Link>
            </Button>
          </div>
        }
      />

      <div className="bg-card border rounded-lg p-8">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
            <EyeIcon className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Formulário de Edição de Produto</h3>
          <p className="text-muted-foreground mb-4">
            Aqui será implementado o formulário de edição de produto
          </p>
          <p className="text-sm text-muted-foreground">
            Esta página já está integrada com a NavBar através do AppWrapper
          </p>
        </div>
      </div>
    </div>
  );
}
