import { getActiveOrganization } from "@saas/auth/lib/server";
import { getSession } from "@saas/auth/lib/server";
import { isAdmin, isSeller } from "@repo/auth/lib/helper";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	CreditCardIcon,
	BuildingIcon,
	UserIcon,
	ShieldIcon,
	ZapIcon,
	SettingsIcon,
	ArrowRightIcon,
} from "lucide-react";
import Link from "next/link";

export default async function AccountPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const session = await getSession();
	const organization = await getActiveOrganization(organizationSlug);

	if (!session || !organization) {
		return null;
	}

	const userIsOrganizationAdmin = isAdmin(session.user);
	const userIsSeller = isSeller(session.user);

	return (
		<div className="space-y-6">
			{/* Header com informações do usuário */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold">Minha Conta</h1>
					<p className="text-muted-foreground">
						Gerencie suas configurações pessoais e da organização
					</p>
				</div>
				<Badge variant={userIsOrganizationAdmin ? "default" : "secondary"}>
					{userIsOrganizationAdmin ? "Administrador" : "Vendedor"}
				</Badge>
			</div>

			{/* Cards de configurações */}
			<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
				{/* Configurações Pessoais */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Minha Conta</CardTitle>
						<UserIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Gerencie seu perfil, segurança e preferências pessoais
						</CardDescription>
						<div className="space-y-2">
							<Link href={`/app/${organizationSlug}/account/profile`}>
								<Button variant="outline" size="sm" className="w-full justify-between">
									Perfil
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
							<Link href={`/app/${organizationSlug}/account/security`}>
								<Button variant="outline" size="sm" className="w-full justify-between">
									Segurança
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
							<Link href={`/app/${organizationSlug}/account/preferences`}>
								<Button variant="outline" size="sm" className="w-full justify-between">
									Preferências
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>

				{/* Configurações da Organização (apenas admins) */}
				{userIsOrganizationAdmin && (
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Organização</CardTitle>
							<BuildingIcon className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<CardDescription className="mb-4">
								Configure sua organização, membros e integrações
							</CardDescription>
							<div className="space-y-2">
								<Link href={`/app/${organizationSlug}/account/organization`}>
									<Button variant="outline" size="sm" className="w-full justify-between">
										Configurações
										<ArrowRightIcon className="h-4 w-4" />
									</Button>
								</Link>
								<Link href={`/app/${organizationSlug}/account/members`}>
									<Button variant="outline" size="sm" className="w-full justify-between">
										Membros
										<ArrowRightIcon className="h-4 w-4" />
									</Button>
								</Link>
								<Link href={`/app/${organizationSlug}/account/integrations`}>
									<Button variant="outline" size="sm" className="w-full justify-between">
										Integrações
										<ArrowRightIcon className="h-4 w-4" />
									</Button>
								</Link>
							</div>
						</CardContent>
					</Card>
				)}

				{/* Cobrança (apenas admins) */}
				{userIsOrganizationAdmin && (
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Cobrança</CardTitle>
							<CreditCardIcon className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<CardDescription className="mb-4">
								Gerencie planos, TPV e métodos de pagamento
							</CardDescription>
							<div className="space-y-2">
								<Link href={`/app/${organizationSlug}/account/billing`}>
									<Button variant="outline" size="sm" className="w-full justify-between">
										Planos e TPV
										<ArrowRightIcon className="h-4 w-4" />
									</Button>
								</Link>
								<Link href={`/app/${organizationSlug}/account/invoices`}>
									<Button variant="outline" size="sm" className="w-full justify-between">
										Faturas
										<ArrowRightIcon className="h-4 w-4" />
									</Button>
								</Link>
								<Link href={`/app/${organizationSlug}/account/payment-methods`}>
									<Button variant="outline" size="sm" className="w-full justify-between">
										Pagamentos
										<ArrowRightIcon className="h-4 w-4" />
									</Button>
								</Link>
							</div>
						</CardContent>
					</Card>
				)}
			</div>

			{/* Informações adicionais para vendedores */}
			{userIsSeller && !userIsOrganizationAdmin && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<ShieldIcon className="h-5 w-5" />
							Permissões Limitadas
						</CardTitle>
						<CardDescription>
							Como vendedor, você tem acesso limitado às configurações.
							Entre em contato com um administrador para alterações na organização.
						</CardDescription>
					</CardHeader>
				</Card>
			)}
		</div>
	);
}
