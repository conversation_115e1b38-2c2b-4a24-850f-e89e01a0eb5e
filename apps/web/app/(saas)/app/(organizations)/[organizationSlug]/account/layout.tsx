import { getActiveOrganization } from "@saas/auth/lib/server";
import { getSession } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { SidebarContentLayout } from "@saas/shared/components/SidebarContentLayout";
import { SettingsMenu } from "@saas/settings/components/SettingsMenu";
import { UserAvatar } from "@shared/components/UserAvatar";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import { isAdmin, isSeller } from "@repo/auth/lib/helper";
import {
	CreditCardIcon,
	LockKeyholeIcon,
	SettingsIcon,
	BuildingIcon,
	UserCog2Icon,
	TriangleAlertIcon,
	ZapIcon,
} from "lucide-react";
import { getTranslations } from "next-intl/server";
import { notFound } from "next/navigation";
import type { PropsWithChildren } from "react";

interface AccountLayoutProps extends PropsWithChildren {
	params: Promise<{ organizationSlug: string }>;
}

export default async function AccountLayout({
	children,
	params,
}: AccountLayoutProps) {
	const t = await getTranslations();
	const { organizationSlug } = await params;
	const session = await getSession();
	const organization = await getActiveOrganization(organizationSlug);

	if (!session) {
		return notFound();
	}

	if (!organization) {
		return notFound();
	}

	const userIsOrganizationAdmin = isAdmin(session.user);
	const userIsSeller = isSeller(session.user);

	// Menu de configurações baseado no role do usuário
	const accountMenuItems = [
		// Configurações pessoais (todos os usuários)
		{
			title: "Minha Conta",
			avatar: (
				<UserAvatar
					name={session.user.name ?? ""}
					avatarUrl={session.user.image}
				/>
			),
			items: [
				{
					title: "Perfil",
					href: `/app/${organizationSlug}/account/profile`,
					icon: <UserCog2Icon className="size-4 opacity-50" />,
				},
				{
					title: "Segurança",
					href: `/app/${organizationSlug}/account/security`,
					icon: <LockKeyholeIcon className="size-4 opacity-50" />,
				},
				{
					title: "Preferências",
					href: `/app/${organizationSlug}/account/preferences`,
					icon: <SettingsIcon className="size-4 opacity-50" />,
				},
			],
		},
		// Configurações da organização (apenas admins)
		...(userIsOrganizationAdmin
			? [
					{
						title: "Organização",
						avatar: (
							<OrganizationLogo
								name={organization.name}
								logoUrl={organization.logo}
							/>
						),
						items: [
							{
								title: "Configurações Gerais",
								href: `/app/${organizationSlug}/account/organization`,
								icon: <BuildingIcon className="size-4 opacity-50" />,
							},
							{
								title: "Membros",
								href: `/app/${organizationSlug}/account/members`,
								icon: <UserCog2Icon className="size-4 opacity-50" />,
							},
							{
								title: "Integrações",
								href: `/app/${organizationSlug}/account/integrations`,
								icon: <ZapIcon className="size-4 opacity-50" />,
							},
						],
					},
				]
			: []),
		// Cobrança (apenas admins)
		...(userIsOrganizationAdmin
			? [
					{
						title: "Cobrança",
						avatar: (
							<div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
								<CreditCardIcon className="h-4 w-4 text-primary" />
							</div>
						),
						items: [
							{
								title: "Planos e TPV",
								href: `/app/${organizationSlug}/account/billing`,
								icon: <CreditCardIcon className="size-4 opacity-50" />,
							},
							{
								title: "Faturas",
								href: `/app/${organizationSlug}/account/invoices`,
								icon: <CreditCardIcon className="size-4 opacity-50" />,
							},
							{
								title: "Métodos de Pagamento",
								href: `/app/${organizationSlug}/account/payment-methods`,
								icon: <CreditCardIcon className="size-4 opacity-50" />,
							},
						],
					},
				]
			: []),
	];

	return (
		<>
			<PageHeader
				title="Conta"
				subtitle="Gerencie suas configurações pessoais e da organização"
			/>
			<SidebarContentLayout
				sidebar={<SettingsMenu menuItems={accountMenuItems} />}
			>
				{children}
			</SidebarContentLayout>
		</>
	);
}
