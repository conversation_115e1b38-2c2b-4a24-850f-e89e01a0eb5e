"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { Skeleton } from "@ui/components/skeleton";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { useAnalytics, AnalyticsFilters } from "@saas/analytics/hooks/useAnalytics";
import { AnalyticsMetrics } from "@saas/analytics/components/AnalyticsMetrics";
import { AnalyticsFiltersComponent } from "@saas/analytics/components/AnalyticsFilters";
import {
  RevenueChart,
  TransactionsChart,
  PaymentMethodsChart,
  TopProductsChart,
} from "@saas/analytics/components/AnalyticsCharts";
import {
  Activity,
  TrendingUp,
  Users,
  CreditCard,
  Download,
  Zap,
  Receipt,
  Refresh<PERSON><PERSON>,
  Setting<PERSON>,
} from "lucide-react";

interface AnalyticsClientProps {
  organization: {
    id: string;
    name: string;
    slug: string;
  };
}

export function AnalyticsClient({ organization }: AnalyticsClientProps) {
  const [filters, setFilters] = useState<AnalyticsFilters>({
    period: "30d",
  });

  const { data: analytics, isLoading, error } = useAnalytics(organization.id, filters);

  const handleFiltersChange = (newFilters: AnalyticsFilters) => {
    setFilters(newFilters);
  };

  const handleExport = () => {
    // Implementar exportação
    console.log("Exporting analytics data...");
  };

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(cents / 100);
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("pt-BR");
  };

  if (error) {
    return (
      <div className="space-y-6 p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-destructive mb-2">
            Erro ao carregar analytics
          </h2>
          <p className="text-muted-foreground">
            Não foi possível carregar os dados de analytics. Tente novamente.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Analytics"
        subtitle="Métricas e insights detalhados da sua organização"
        actions={
          <>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Configurações
            </Button>
          </>
        }
      />

      {/* Filters */}
      <AnalyticsFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onExport={handleExport}
        isLoading={isLoading}
      />

      {/* Period Info */}
      {analytics && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Período:</span>
                <span className="text-sm text-muted-foreground">
                  {formatDate(analytics.period.from)} - {formatDate(analytics.period.to)}
                </span>
              </div>
              <Badge variant="secondary">
                {analytics.period.type === "7d" && "Últimos 7 dias"}
                {analytics.period.type === "30d" && "Últimos 30 dias"}
                {analytics.period.type === "90d" && "Últimos 90 dias"}
                {analytics.period.type === "1y" && "Último ano"}
                {analytics.period.type === "all" && "Todo o período"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metrics */}
      <AnalyticsMetrics data={analytics?.metrics} isLoading={isLoading} />

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="revenue">Receita</TabsTrigger>
          <TabsTrigger value="transactions">Transações</TabsTrigger>
          <TabsTrigger value="products">Produtos</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {isLoading ? (
              <>
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[400px] w-full" />
              </>
            ) : (
              <>
                <RevenueChart data={analytics?.charts.monthlyRevenue || []} />
                <TransactionsChart data={analytics?.charts.monthlyRevenue || []} />
              </>
            )}
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {isLoading ? (
              <>
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[400px] w-full" />
              </>
            ) : (
              <>
                <PaymentMethodsChart data={analytics?.charts.paymentMethods || []} />
                <TopProductsChart data={analytics?.charts.topProducts || []} />
              </>
            )}
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle>Transações Recentes</CardTitle>
                <CardDescription>
                  Últimas transações processadas
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <Skeleton key={i} className="h-16 w-full" />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {analytics?.recentTransactions?.length ? (
                      analytics.recentTransactions.map((transaction) => (
                        <div
                          key={transaction.id}
                          className="flex items-center justify-between p-3 rounded-lg border bg-muted/50"
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <p className="font-medium text-sm">{transaction.customer}</p>
                              <Badge
                                variant={transaction.status === "COMPLETED" ? "default" : "secondary"}
                              >
                                {transaction.status === "COMPLETED" ? "Pago" : "Pendente"}
                              </Badge>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {transaction.id} • {transaction.method} • {formatDate(transaction.date)}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">
                              {formatCurrency(transaction.amount)}
                            </p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        Nenhuma transação encontrada
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Methods Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Métodos de Pagamento</CardTitle>
                <CardDescription>
                  Distribuição por método de pagamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <Skeleton key={i} className="h-12 w-full" />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {analytics?.charts.paymentMethods?.map((method, index) => {
                      const getIcon = (methodName: string) => {
                        switch (methodName) {
                          case "PIX":
                            return <Zap className="h-4 w-4 text-green-600" />;
                          case "CREDIT_CARD":
                            return <CreditCard className="h-4 w-4 text-blue-600" />;
                          case "BOLETO":
                            return <Receipt className="h-4 w-4 text-orange-600" />;
                          default:
                            return <CreditCard className="h-4 w-4 text-gray-600" />;
                        }
                      };

                      return (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getIcon(method.method)}
                            <span className="text-sm font-medium">
                              {method.method === "PIX" && "PIX"}
                              {method.method === "CREDIT_CARD" && "Cartão de Crédito"}
                              {method.method === "BOLETO" && "Boleto"}
                              {!["PIX", "CREDIT_CARD", "BOLETO"].includes(method.method) && method.method}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-20 bg-muted rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-300 ${
                                  method.method === "PIX" ? "bg-green-500" :
                                  method.method === "CREDIT_CARD" ? "bg-blue-500" :
                                  "bg-orange-500"
                                }`}
                                style={{ width: `${method.percentage}%` }}
                              />
                            </div>
                            <span className="text-sm text-muted-foreground w-12 text-right">
                              {method.percentage}%
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {isLoading ? (
              <>
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[400px] w-full" />
              </>
            ) : (
              <>
                <TopProductsChart data={analytics?.charts.topProducts || []} />
                <PaymentMethodsChart data={analytics?.charts.paymentMethods || []} />
              </>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesse rapidamente as funcionalidades mais usadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              <Activity className="h-4 w-4 mr-2" />
              Relatórios Detalhados
            </Button>
            <Button variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              Comparar Períodos
            </Button>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Análise de Clientes
            </Button>
            <Button variant="outline" size="sm">
              <CreditCard className="h-4 w-4 mr-2" />
              Métodos de Pagamento
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
