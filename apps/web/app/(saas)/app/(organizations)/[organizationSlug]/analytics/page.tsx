import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { AnalyticsClient } from "./components/AnalyticsClient";

export default async function AnalyticsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return <AnalyticsClient organization={organization} />;
}
