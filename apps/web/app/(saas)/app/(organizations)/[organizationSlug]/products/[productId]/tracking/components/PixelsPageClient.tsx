"use client";

import { useState, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Badge } from "@ui/components/badge";
import {
  PlusIcon,
  BarChart3Icon,
  MoreHorizontalIcon,
  EditIcon,
  TrashIcon,
  EyeIcon,
  EyeOffIcon,
  SearchIcon,
  FilterIcon,
  SortAscIcon,
  SortDescIcon,
  CheckCircleIcon,
  XCircleIcon,
  TargetIcon,
  TrendingUpIcon
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@ui/components/alert-dialog";
import { cn } from "@ui/lib";
import { CreatePixelModal } from "./CreatePixelModal";
import {
  usePixelsByProduct,
  useDeletePixel,
  useUpdatePixel,
  getPlatformLabel,
  getPlatformColor,
  type Pixel
} from "@saas/products/hooks/usePixels";

interface PixelsPageClientProps {
  organizationSlug: string;
  productId: string;
  product: any;
}

type SortField = "name" | "platform" | "createdAt" | "isActive";
type SortOrder = "asc" | "desc";

interface PixelFilters {
  search: string;
  platform: string;
  status: string;
}

interface PixelSort {
  field: SortField;
  order: SortOrder;
}



export function PixelsPageClient({
  organizationSlug,
  productId,
  product,
}: PixelsPageClientProps) {
  const [filters, setFilters] = useState<PixelFilters>({
    search: "",
    platform: "all",
    status: "all",
  });

  const [sort, setSort] = useState<PixelSort>({
    field: "createdAt",
    order: "desc",
  });

  // Fetch pixels data
  const { data: pixels = [], isLoading, error } = usePixelsByProduct(productId);
  const deletePixelMutation = useDeletePixel();
  const updatePixelMutation = useUpdatePixel();

  // Filter and sort pixels
  const filteredAndSortedPixels = useMemo(() => {
    let filtered = pixels;

    // Apply search filter
    if (filters.search) {
      filtered = filtered.filter(pixel =>
        pixel.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        pixel.pixelId.toLowerCase().includes(filters.search.toLowerCase())
      );
    }

    // Apply platform filter
    if (filters.platform !== "all") {
      filtered = filtered.filter(pixel => pixel.platform === filters.platform);
    }

    // Apply status filter
    if (filters.status !== "all") {
      const isActive = filters.status === "active";
      filtered = filtered.filter(pixel => pixel.isActive === isActive);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sort.field];
      let bValue: any = b[sort.field];

      if (sort.field === "createdAt") {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (typeof aValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sort.order === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [pixels, filters, sort]);

  // Handle actions
  const handleDeletePixel = async (pixelId: string) => {
    try {
      await deletePixelMutation.mutateAsync(pixelId);
    } catch (error) {
      console.error("Error deleting pixel:", error);
    }
  };

  const handleTogglePixel = async (pixelId: string, currentStatus: boolean) => {
    try {
      await updatePixelMutation.mutateAsync({
        id: pixelId,
        data: { isActive: !currentStatus },
      });
    } catch (error) {
      console.error("Error toggling pixel:", error);
    }
  };

  const handleSort = (field: SortField) => {
    setSort(prev => ({
      field,
      order: prev.field === field && prev.order === "asc" ? "desc" : "asc",
    }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // Statistics
  const activePixels = pixels.filter(pixel => pixel.isActive);
  const totalPixels = pixels.length;
  const totalEvents = pixels.reduce((sum, pixel) => sum + pixel.events.length, 0);

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Produtos</span>
            <span>/</span>
            <span>{product.name.length > 20 ? `${product.name.substring(0, 20)}...` : product.name}</span>
            <span>/</span>
            <span className="text-foreground font-medium">Pixels de rastreamento</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm font-medium">Pessoa Física</div>
              <div className="text-xs text-muted-foreground">PF</div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium">R$ 0,00 faturado</div>
              <div className="text-xs text-muted-foreground">0 venda realizada</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Pixels de rastreamento</h1>
              <p className="text-muted-foreground">Configure pixels de rastreamento para analytics e remarketing</p>
            </div>
            <CreatePixelModal
              organizationSlug={organizationSlug}
              productId={productId}
              product={product}
            />
          </div>

          {/* Estatísticas */}
          {pixels.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total de Pixels</CardTitle>
                  <TargetIcon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalPixels}</div>
                  <p className="text-xs text-muted-foreground">
                    Pixels configurados
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pixels Ativos</CardTitle>
                  <CheckCircleIcon className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{activePixels.length}</div>
                  <p className="text-xs text-muted-foreground">
                    {totalPixels > 0 ? Math.round((activePixels.length / totalPixels) * 100) : 0}% do total
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Eventos Configurados</CardTitle>
                  <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalEvents}</div>
                  <p className="text-xs text-muted-foreground">
                    Eventos de rastreamento
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Plataformas</CardTitle>
                  <BarChart3Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {new Set(pixels.map(p => p.platform)).size}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Diferentes plataformas
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Filtros e Busca */}
          {pixels.length > 0 && (
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por nome ou ID do pixel..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>

              <Select
                value={filters.platform}
                onValueChange={(value) => setFilters(prev => ({ ...prev, platform: value }))}
              >
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Plataforma" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as plataformas</SelectItem>
                  <SelectItem value="facebook">Facebook</SelectItem>
                  <SelectItem value="google">Google</SelectItem>
                  <SelectItem value="tiktok">TikTok</SelectItem>
                  <SelectItem value="custom">Personalizado</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.status}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger className="w-full sm:w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="active">Ativos</SelectItem>
                  <SelectItem value="inactive">Inativos</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {isLoading ? (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Carregando pixels...</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : error ? (
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <XCircleIcon className="h-8 w-8 text-destructive mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Erro ao carregar pixels</h3>
                  <p className="text-muted-foreground mb-4">
                    Ocorreu um erro ao carregar os pixels. Tente novamente.
                  </p>
                  <Button onClick={() => window.location.reload()}>
                    Tentar novamente
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : pixels.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Pixels Configurados</CardTitle>
                <CardDescription>
                  Gerencie seus pixels de rastreamento do Facebook, Google e outras plataformas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BarChart3Icon className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Nenhum pixel configurado</h3>
                  <p className="text-muted-foreground mb-4">
                    Configure pixels de rastreamento para acompanhar conversões e otimizar campanhas
                  </p>
                  <CreatePixelModal 
                    organizationSlug={organizationSlug}
                    productId={productId}
                    product={product}
                    variant="default"
                    buttonText="Configurar Primeiro Pixel"
                  />
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>
                  Pixels Configurados ({filteredAndSortedPixels.length}
                  {filteredAndSortedPixels.length !== pixels.length && ` de ${pixels.length}`})
                </CardTitle>
                <CardDescription>
                  Gerencie seus pixels de rastreamento do Facebook, Google e outras plataformas
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredAndSortedPixels.length === 0 ? (
                  <div className="text-center py-12">
                    <FilterIcon className="h-8 w-8 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Nenhum pixel encontrado</h3>
                    <p className="text-muted-foreground mb-4">
                      Tente ajustar os filtros ou criar um novo pixel
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => setFilters({ search: "", platform: "all", status: "all" })}
                    >
                      Limpar filtros
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSort("name")}
                        >
                          <div className="flex items-center gap-2">
                            Nome
                            {sort.field === "name" && (
                              sort.order === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSort("platform")}
                        >
                          <div className="flex items-center gap-2">
                            Plataforma
                            {sort.field === "platform" && (
                              sort.order === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead>ID do Pixel</TableHead>
                        <TableHead>Eventos</TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSort("isActive")}
                        >
                          <div className="flex items-center gap-2">
                            Status
                            {sort.field === "isActive" && (
                              sort.order === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSort("createdAt")}
                        >
                          <div className="flex items-center gap-2">
                            Criado em
                            {sort.field === "createdAt" && (
                              sort.order === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead className="w-[50px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedPixels.map((pixel) => (
                      <TableRow key={pixel.id}>
                        <TableCell className="font-medium">
                          {pixel.name}
                        </TableCell>
                        <TableCell>
                          <Badge className={platformColors[pixel.platform]}>
                            {platformLabels[pixel.platform]}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <code className="text-sm bg-muted px-2 py-1 rounded">
                            {pixel.pixelId}
                          </code>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1 flex-wrap">
                            {pixel.events.slice(0, 2).map((event) => (
                              <Badge key={event} variant="secondary" className="text-xs">
                                {event}
                              </Badge>
                            ))}
                            {pixel.events.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{pixel.events.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={pixel.isActive ? "default" : "secondary"}>
                            {pixel.isActive ? "Ativo" : "Inativo"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDate(pixel.createdAt)}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontalIcon className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <EditIcon className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleTogglePixel(pixel.id, pixel.isActive)}
                                disabled={updatePixelMutation.isPending}
                              >
                                {pixel.isActive ? (
                                  <>
                                    <EyeOffIcon className="h-4 w-4 mr-2" />
                                    Desativar
                                  </>
                                ) : (
                                  <>
                                    <EyeIcon className="h-4 w-4 mr-2" />
                                    Ativar
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <DropdownMenuItem
                                    className="text-destructive"
                                    onSelect={(e) => e.preventDefault()}
                                  >
                                    <TrashIcon className="h-4 w-4 mr-2" />
                                    Excluir
                                  </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Tem certeza que deseja excluir o pixel "{pixel.name}"?
                                      Esta ação não pode ser desfeita e o rastreamento será interrompido.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDeletePixel(pixel.id)}
                                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      disabled={deletePixelMutation.isPending}
                                    >
                                      {deletePixelMutation.isPending ? "Excluindo..." : "Excluir"}
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
