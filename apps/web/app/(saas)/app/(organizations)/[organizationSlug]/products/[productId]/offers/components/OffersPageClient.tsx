"use client";

import { useState, useMemo } from "react";
import { useOffers, useDeleteOffer, formatOfferPrice, isOfferActive } from "@saas/products/hooks/useOffers";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { cn } from "@ui/lib";
import {
  PlusIcon,
  MoreHorizontalIcon,
  EditIcon,
  TrashIcon,
  CopyIcon,
  EyeIcon,
  CalendarIcon,
  DollarSignIcon,
  SearchIcon,
  FilterIcon,
  SortAscIcon,
  SortDescIcon,
  TrendingUpIcon,
  TargetIcon,
  CheckCircleIcon,
  XCircleIcon,
  LayoutGridIcon,
  LayoutListIcon
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@ui/components/alert-dialog";
import { CreateOfferModal } from "./CreateOfferModal";
import { OfferCard } from "./OfferCard";
import type { Offer } from "@saas/products/hooks/useOffers";

interface OffersPageClientProps {
  organizationSlug: string;
  productId: string;
  product: any;
}

const offerTypeLabels: Record<string, string> = {
  "single-price": "Preço Único",
  "subscription": "Assinatura",
  "ORDER_BUMP": "Order Bump",
  "UPSELL": "Upsell",
  "DOWNSELL": "Downsell",
};

const offerTypeColors: Record<string, string> = {
  "single-price": "bg-blue-100 text-blue-800",
  "subscription": "bg-purple-100 text-purple-800",
  "ORDER_BUMP": "bg-green-100 text-green-800",
  "UPSELL": "bg-orange-100 text-orange-800",
  "DOWNSELL": "bg-red-100 text-red-800",
};

type SortField = "name" | "type" | "valueCents" | "createdAt" | "isActive";
type SortDirection = "asc" | "desc";

export function OffersPageClient({
  organizationSlug,
  productId,
  product,
}: OffersPageClientProps) {
  const { data: offers, isLoading, error } = useOffers(productId);
  const deleteOfferMutation = useDeleteOffer();
  const [deletingOfferId, setDeletingOfferId] = useState<string | null>(null);

  // Filtros e busca
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortField, setSortField] = useState<SortField>("createdAt");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [viewMode, setViewMode] = useState<"table" | "cards">("table");

  // Filtros e ordenação
  const filteredAndSortedOffers = useMemo(() => {
    if (!offers) return [];

    let filtered = offers.filter(offer => {
      const matchesSearch = offer.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = typeFilter === "all" || offer.type === typeFilter;
      const matchesStatus = statusFilter === "all" ||
        (statusFilter === "active" && isOfferActive(offer)) ||
        (statusFilter === "inactive" && !isOfferActive(offer));

      return matchesSearch && matchesType && matchesStatus;
    });

    // Ordenação
    filtered.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === "createdAt") {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      } else if (sortField === "isActive") {
        aValue = isOfferActive(a) ? 1 : 0;
        bValue = isOfferActive(b) ? 1 : 0;
      } else if (typeof aValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [offers, searchTerm, typeFilter, statusFilter, sortField, sortDirection]);

  // Estatísticas
  const stats = useMemo(() => {
    if (!offers) return { total: 0, active: 0, inactive: 0, totalValue: 0 };

    const active = offers.filter(offer => isOfferActive(offer));
    const inactive = offers.filter(offer => !isOfferActive(offer));
    const totalValue = offers.reduce((sum, offer) => sum + offer.valueCents, 0);

    return {
      total: offers.length,
      active: active.length,
      inactive: inactive.length,
      totalValue,
    };
  }, [offers]);

  const handleDeleteOffer = async (offerId: string) => {
    setDeletingOfferId(offerId);
    try {
      await deleteOfferMutation.mutateAsync(offerId);
    } catch (error) {
      console.error("Error deleting offer:", error);
    } finally {
      setDeletingOfferId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const copyOfferUrl = async (offerId: string) => {
    const url = `${window.location.origin}/checkout/${productId}?offer=${offerId}&org=${organizationSlug}`;
    try {
      await navigator.clipboard.writeText(url);
      // You might want to add a toast notification here
    } catch (error) {
      console.error("Error copying URL:", error);
    }
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando ofertas...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-destructive mb-4">Erro ao carregar ofertas</p>
          <Button onClick={() => window.location.reload()}>Tentar novamente</Button>
        </div>
      </div>
    );
  }

  if (!offers || offers.length === 0) {
    return (
      <div className="space-y-6">
        {/* Estatísticas vazias */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TargetIcon className="h-8 w-8 text-muted-foreground" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Total de Ofertas</p>
                  <p className="text-2xl font-bold">0</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Ofertas Ativas</p>
                  <p className="text-2xl font-bold">0</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircleIcon className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Ofertas Inativas</p>
                  <p className="text-2xl font-bold">0</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSignIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Valor Total</p>
                  <p className="text-2xl font-bold">R$ 0,00</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Ofertas do Produto</CardTitle>
            <CardDescription>
              Configure ofertas especiais para aumentar suas vendas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <PlusIcon className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Nenhuma oferta criada</h3>
              <p className="text-muted-foreground mb-6">
                Crie sua primeira oferta para começar a vender mais
              </p>
              <div className="flex justify-center">
                <CreateOfferModal
                  organizationSlug={organizationSlug}
                  productId={productId}
                  product={product}
                  variant="default"
                  buttonText="Criar Primeira Oferta"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TargetIcon className="h-8 w-8 text-muted-foreground" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Total de Ofertas</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Ofertas Ativas</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircleIcon className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Ofertas Inativas</p>
                <p className="text-2xl font-bold">{stats.inactive}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSignIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Valor Total</p>
                <p className="text-2xl font-bold">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL',
                  }).format(stats.totalValue / 100)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros e Busca */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Filtros e Busca</CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "table" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("table")}
              >
                <LayoutListIcon className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "cards" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("cards")}
              >
                <LayoutGridIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Buscar ofertas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filtrar por tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os tipos</SelectItem>
                <SelectItem value="single-price">Preço Único</SelectItem>
                <SelectItem value="subscription">Assinatura</SelectItem>
                <SelectItem value="ORDER_BUMP">Order Bump</SelectItem>
                <SelectItem value="UPSELL">Upsell</SelectItem>
                <SelectItem value="DOWNSELL">Downsell</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filtrar por status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os status</SelectItem>
                <SelectItem value="active">Ativas</SelectItem>
                <SelectItem value="inactive">Inativas</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Ofertas do Produto ({filteredAndSortedOffers.length})</CardTitle>
          <CardDescription>
            Gerencie as ofertas e promoções do seu produto
          </CardDescription>
        </CardHeader>
        <CardContent>
          {viewMode === "table" ? (
            <div className="rounded-md border">
              <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("name")}
                  >
                    <div className="flex items-center gap-2">
                      Nome
                      {sortField === "name" && (
                        sortDirection === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("type")}
                  >
                    <div className="flex items-center gap-2">
                      Tipo
                      {sortField === "type" && (
                        sortDirection === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("valueCents")}
                  >
                    <div className="flex items-center gap-2">
                      Preço
                      {sortField === "valueCents" && (
                        sortDirection === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("isActive")}
                  >
                    <div className="flex items-center gap-2">
                      Status
                      {sortField === "isActive" && (
                        sortDirection === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("createdAt")}
                  >
                    <div className="flex items-center gap-2">
                      Criado em
                      {sortField === "createdAt" && (
                        sortDirection === "asc" ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="w-[70px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedOffers.map((offer) => (
                  <TableRow key={offer.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <DollarSignIcon className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{offer.name}</div>
                          {offer.targetProduct && (
                            <div className="text-xs text-muted-foreground">
                              → {offer.targetProduct.name}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={cn("font-medium", offerTypeColors[offer.type] || "bg-gray-100 text-gray-800")}
                      >
                        {offerTypeLabels[offer.type] || offer.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-mono font-medium">
                          {formatOfferPrice(offer)}
                        </span>
                        {offer.settings?.isFree && (
                          <span className="text-xs text-green-600 font-medium">Gratuita</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={isOfferActive(offer) ? "default" : "secondary"}
                        className={cn(
                          isOfferActive(offer)
                            ? "bg-green-100 text-green-800 hover:bg-green-100"
                            : "bg-gray-100 text-gray-800 hover:bg-gray-100"
                        )}
                      >
                        <div className="flex items-center gap-1">
                          {isOfferActive(offer) ? (
                            <CheckCircleIcon className="h-3 w-3" />
                          ) : (
                            <XCircleIcon className="h-3 w-3" />
                          )}
                          {isOfferActive(offer) ? "Ativa" : "Inativa"}
                        </div>
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <CalendarIcon className="h-3 w-3" />
                        {formatDate(offer.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontalIcon className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => copyOfferUrl(offer.id)}>
                            <CopyIcon className="h-4 w-4 mr-2" />
                            Copiar URL
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <EyeIcon className="h-4 w-4 mr-2" />
                            Visualizar
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <EditIcon className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem 
                                className="text-destructive focus:text-destructive"
                                onSelect={(e) => e.preventDefault()}
                              >
                                <TrashIcon className="h-4 w-4 mr-2" />
                                Excluir
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Excluir Oferta</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Tem certeza que deseja excluir a oferta "{offer.name}"? 
                                  Esta ação não pode ser desfeita.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteOffer(offer.id)}
                                  disabled={deletingOfferId === offer.id}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  {deletingOfferId === offer.id ? "Excluindo..." : "Excluir"}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAndSortedOffers.map((offer) => (
                <OfferCard
                  key={offer.id}
                  offer={offer}
                  organizationSlug={organizationSlug}
                  productId={productId}
                  onDelete={handleDeleteOffer}
                  isDeleting={deletingOfferId === offer.id}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
