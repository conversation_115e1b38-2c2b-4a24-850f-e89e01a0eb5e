"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { 
  MoreHorizontalIcon,
  EditIcon,
  TrashIcon,
  CopyIcon,
  EyeIcon,
  CalendarIcon,
  DollarSignIcon,
  CheckCircleIcon,
  XCircleIcon,
  TrendingUpIcon,
  TargetIcon,
  ExternalLinkIcon
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@ui/components/alert-dialog";
import { formatOfferPrice, isOfferActive } from "@saas/products/hooks/useOffers";
import type { Offer } from "@saas/products/hooks/useOffers";

interface OfferCardProps {
  offer: Offer;
  organizationSlug: string;
  productId: string;
  onDelete: (offerId: string) => void;
  isDeleting: boolean;
}

const offerTypeLabels: Record<string, string> = {
  "single-price": "Preço Único",
  "subscription": "Assinatura",
  "ORDER_BUMP": "Order Bump",
  "UPSELL": "Upsell",
  "DOWNSELL": "Downsell",
};

const offerTypeColors: Record<string, string> = {
  "single-price": "bg-blue-100 text-blue-800",
  "subscription": "bg-purple-100 text-purple-800",
  "ORDER_BUMP": "bg-green-100 text-green-800",
  "UPSELL": "bg-orange-100 text-orange-800",
  "DOWNSELL": "bg-red-100 text-red-800",
};

const offerTypeIcons: Record<string, React.ReactNode> = {
  "single-price": <DollarSignIcon className="h-4 w-4" />,
  "subscription": <TrendingUpIcon className="h-4 w-4" />,
  "ORDER_BUMP": <TargetIcon className="h-4 w-4" />,
  "UPSELL": <TrendingUpIcon className="h-4 w-4" />,
  "DOWNSELL": <TrendingUpIcon className="h-4 w-4" />,
};

export function OfferCard({ 
  offer, 
  organizationSlug, 
  productId, 
  onDelete, 
  isDeleting 
}: OfferCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const copyOfferUrl = async () => {
    const url = `${window.location.origin}/checkout/${productId}?offer=${offer.id}&org=${organizationSlug}`;
    try {
      await navigator.clipboard.writeText(url);
      // You might want to add a toast notification here
    } catch (error) {
      console.error("Error copying URL:", error);
    }
  };

  const handleDelete = () => {
    onDelete(offer.id);
    setShowDeleteDialog(false);
  };

  const isActive = isOfferActive(offer);

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-md",
      isActive ? "border-green-200 bg-green-50/30" : "border-gray-200"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              "p-2 rounded-lg",
              offerTypeColors[offer.type] || "bg-gray-100 text-gray-800"
            )}>
              {offerTypeIcons[offer.type] || <DollarSignIcon className="h-4 w-4" />}
            </div>
            <div>
              <CardTitle className="text-lg">{offer.name}</CardTitle>
              <CardDescription className="flex items-center gap-2 mt-1">
                <Badge 
                  variant="outline" 
                  className={cn("text-xs", offerTypeColors[offer.type] || "bg-gray-100 text-gray-800")}
                >
                  {offerTypeLabels[offer.type] || offer.type}
                </Badge>
                <Badge 
                  variant={isActive ? "default" : "secondary"}
                  className={cn(
                    "text-xs",
                    isActive 
                      ? "bg-green-100 text-green-800 hover:bg-green-100" 
                      : "bg-gray-100 text-gray-800 hover:bg-gray-100"
                  )}
                >
                  <div className="flex items-center gap-1">
                    {isActive ? (
                      <CheckCircleIcon className="h-3 w-3" />
                    ) : (
                      <XCircleIcon className="h-3 w-3" />
                    )}
                    {isActive ? "Ativa" : "Inativa"}
                  </div>
                </Badge>
              </CardDescription>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={copyOfferUrl}>
                <CopyIcon className="h-4 w-4 mr-2" />
                Copiar URL
              </DropdownMenuItem>
              <DropdownMenuItem>
                <ExternalLinkIcon className="h-4 w-4 mr-2" />
                Visualizar
              </DropdownMenuItem>
              <DropdownMenuItem>
                <EditIcon className="h-4 w-4 mr-2" />
                Editar
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-destructive focus:text-destructive"
                onClick={() => setShowDeleteDialog(true)}
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                Excluir
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Preço */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Preço:</span>
            <div className="text-right">
              <span className="font-mono font-bold text-lg">
                {formatOfferPrice(offer)}
              </span>
              {offer.settings?.isFree && (
                <div className="text-xs text-green-600 font-medium">Gratuita</div>
              )}
            </div>
          </div>

          {/* Produto alvo (para upsells/downsells) */}
          {offer.targetProduct && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Produto alvo:</span>
              <span className="text-sm font-medium">{offer.targetProduct.name}</span>
            </div>
          )}

          {/* Data de criação */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Criado em:</span>
            <div className="flex items-center gap-1 text-sm">
              <CalendarIcon className="h-3 w-3 text-muted-foreground" />
              {formatDate(offer.createdAt)}
            </div>
          </div>

          {/* Configurações especiais */}
          {offer.settings && (
            <div className="pt-2 border-t">
              <div className="flex flex-wrap gap-2">
                {offer.settings.orderBumps && (
                  <Badge variant="outline" className="text-xs">
                    Order Bumps
                  </Badge>
                )}
                {offer.settings.backRedirect?.enabled && (
                  <Badge variant="outline" className="text-xs">
                    Redirect Volta
                  </Badge>
                )}
                {offer.settings.postApprovalRedirect?.enabled && (
                  <Badge variant="outline" className="text-xs">
                    Redirect Sucesso
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>

      {/* Dialog de confirmação de exclusão */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Oferta</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a oferta "{offer.name}"? 
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Excluindo..." : "Excluir"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
