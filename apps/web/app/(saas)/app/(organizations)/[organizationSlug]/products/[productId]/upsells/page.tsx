import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { PlusIcon, TrendingUpIcon } from "lucide-react";
import { ProductPageHeader } from "../components/ProductPageHeader";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function UpsellsPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <ProductPageHeader
        productName={product.name}
        currentPage="Upsells e Mais"
        productStatus={product.status}
        productType={product.type}
        productId={product.id}
      >
        <Button>
          <PlusIcon className="h-4 w-4 mr-2" />
          Nova Oferta
        </Button>
      </ProductPageHeader>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Upsells e Mais</h1>
            <p className="text-muted-foreground">Configure ofertas complementares para aumentar o ticket médio</p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Ofertas Complementares</CardTitle>
              <CardDescription>
                Configure upsells, downsells e ofertas bônus para maximizar suas vendas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUpIcon className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Nenhuma oferta complementar configurada</h3>
                <p className="text-muted-foreground mb-4">
                  Configure ofertas de upsell e downsell para aumentar sua receita
                </p>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Recursos disponíveis:
                  </p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Ofertas de upsell (produtos complementares)</li>
                    <li>• Ofertas de downsell (alternativas mais baratas)</li>
                    <li>• Sequência automática de ofertas</li>
                    <li>• Relatórios de conversão</li>
                  </ul>
                  <div className="pt-4 flex justify-center">
                    <Button disabled>
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Em Desenvolvimento
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
