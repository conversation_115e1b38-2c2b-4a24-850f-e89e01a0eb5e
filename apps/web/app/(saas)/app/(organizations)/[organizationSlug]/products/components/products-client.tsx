"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { PageLayout } from "@ui/components/page-layout";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Filters, FilterConfig } from "@ui/components/filters";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@ui/components/sheet";
import { ProductSkeleton } from "@ui/components/product-skeleton";
import {
	PlusIcon,
	EyeIcon,
	MoreHorizontalIcon,
	ImageIcon,
	UsersIcon,
	DollarSignIcon,
	TrendingUpIcon,
	CalendarIcon,
	StarIcon,
	PlayCircleIcon,
	BookOpenIcon,
	GraduationCapIcon,
	PackageIcon,
	CopyIcon,
	ArchiveIcon,
	TrashIcon,
	Settings2Icon,
	SettingsIcon,
	DownloadIcon,
	FilterIcon,
	ArrowUpIcon,
	ArrowDownIcon,
	TrendingDownIcon,
	SearchIcon,
	XIcon
} from "lucide-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { useProducts, useDeleteProduct, useUpdateProductStatus } from "@saas/products/hooks/useProductsApi";
import { Product } from "@saas/products/hooks/useProductsApi";
import { SimpleCreateProductSheet } from "./SimpleCreateProductSheet";
import { formatDistanceToNow } from "date-fns";

interface ProductsClientProps {
	organizationId: string;
	userId: string;
	organizationSlug?: string;
}

export function ProductsClient({ organizationId, userId, organizationSlug }: ProductsClientProps) {
	const router = useRouter();
	const searchParams = useSearchParams();

	const [search, setSearch] = useState("");
	const [filters, setFilters] = useState<Record<string, string | undefined>>({});
	const [page, setPage] = useState(1);
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

	// Initialize state from URL params
	useEffect(() => {
		const searchTerm = searchParams.get('search') || "";
		const status = searchParams.get('status') || undefined;
		const type = searchParams.get('type') || undefined;
		const pageParam = parseInt(searchParams.get('page') || "1", 10);

		setSearch(searchTerm);
		setFilters({ status, type });
		setPage(pageParam);
	}, [searchParams]);

	// Update URL when state changes
	const updateURL = (newSearch?: string, newFilters?: Record<string, string | undefined>, newPage?: number) => {
		const params = new URLSearchParams();

		const currentSearch = newSearch !== undefined ? newSearch : search;
		const currentFilters = newFilters !== undefined ? newFilters : filters;
		const currentPage = newPage !== undefined ? newPage : page;

		if (currentSearch) params.set('search', currentSearch);
		if (currentFilters.status) params.set('status', currentFilters.status);
		if (currentFilters.type) params.set('type', currentFilters.type);
		if (currentPage > 1) params.set('page', currentPage.toString());

		const url = `/app/${organizationSlug}/products${params.toString() ? `?${params.toString()}` : ''}`;
		router.push(url, { scroll: false });
	};

	// Filter configuration
	const filterConfigs: FilterConfig[] = [
		{
			key: 'status',
			label: 'Status',
			placeholder: 'Todos os status',
			options: [
				{ value: 'PUBLISHED', label: 'Publicado' },
				{ value: 'DRAFT', label: 'Rascunho' },
				{ value: 'ARCHIVED', label: 'Arquivado' },
				{ value: 'SUSPENDED', label: 'Suspenso' }
			]
		},
		{
			key: 'type',
			label: 'Tipo',
			placeholder: 'Todos os tipos',
			options: [
				{ value: 'COURSE', label: 'Curso' },
				{ value: 'EBOOK', label: 'E-book' },
				{ value: 'MENTORING', label: 'Mentoria' },
				{ value: 'SUBSCRIPTION', label: 'Assinatura' },
				{ value: 'BUNDLE', label: 'Pacote' }
			]
		}
	];


	const { data: productsData, isLoading, error } = useProducts(organizationId, {
		page,
		limit: 10,
		search: search || undefined,
		status: filters.status as any,
		type: filters.type as any,
	});


	const deleteProduct = useDeleteProduct();
	const updateStatus = useUpdateProductStatus();

	const products = productsData?.products || [];
	const pagination = productsData?.pagination;

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'default';
			case 'DRAFT':
				return 'secondary';
			case 'ARCHIVED':
				return 'outline';
			case 'SUSPENDED':
				return 'destructive';
			default:
				return 'outline';
		}
	};

	const getStatusLabel = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'Publicado';
			case 'DRAFT':
				return 'Rascunho';
			case 'ARCHIVED':
				return 'Arquivado';
			case 'SUSPENDED':
				return 'Suspenso';
			default:
				return status;
		}
	};

	const getTypeLabel = (type: string) => {
		switch (type) {
			case 'COURSE':
				return 'Curso';
			case 'EBOOK':
				return 'E-book';
			case 'MENTORING':
				return 'Mentoria';
			case 'SUBSCRIPTION':
				return 'Assinatura';
			case 'BUNDLE':
				return 'Pacote';
			default:
				return type;
		}
	};

	const getTypeIcon = (type: string) => {
		switch (type) {
			case 'COURSE':
				return GraduationCapIcon;
			case 'EBOOK':
				return BookOpenIcon;
			case 'MENTORING':
				return UsersIcon;
			case 'SUBSCRIPTION':
				return PlayCircleIcon;
			case 'BUNDLE':
				return PackageIcon;
			default:
				return ImageIcon;
		}
	};

	const formatCurrency = (cents: number, currency: string = 'BRL') => {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency,
		}).format(cents / 100);
	};

	const formatDate = (date: string | Date) => {
		return formatDistanceToNow(new Date(date), {
			addSuffix: true
		});
	};

	// Componente para cards de métricas melhorados
	const MetricCard = ({
		title,
		value,
		subtitle,
		icon: Icon,
		trend,
		trendValue,
		className = ""
	}: {
		title: string;
		value: string | number;
		subtitle: string;
		icon: any;
		trend?: "up" | "down" | "neutral";
		trendValue?: string;
		className?: string;
	}) => {
		const getTrendIcon = () => {
			if (trend === "up") return <ArrowUpIcon className="h-3 w-3" />;
			if (trend === "down") return <ArrowDownIcon className="h-3 w-3" />;
			return <TrendingUpIcon className="h-3 w-3" />;
		};

		const getTrendColor = () => {
			if (trend === "up") return "text-green-600";
			if (trend === "down") return "text-red-600";
			return "text-muted-foreground";
		};

		return (
			<Card className={`relative overflow-hidden shadow-sm border-border/50 hover:shadow-md transition-all duration-200 ${className}`}>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
					<CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
					<div className="p-2 rounded-full bg-primary/10">
						<Icon className="h-4 w-4 text-primary" />
					</div>
				</CardHeader>
				<CardContent>
					<div className="text-2xl font-bold text-foreground">{value}</div>
					<div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
						{trend && trendValue && (
							<div className={`flex items-center gap-1 ${getTrendColor()}`}>
								{getTrendIcon()}
								<span>{trendValue}</span>
							</div>
						)}
						<span className="text-muted-foreground">{subtitle}</span>
					</div>
				</CardContent>
			</Card>
		);
	};

	const handleDeleteProduct = async (productId: string) => {
		if (confirm("Tem certeza que deseja excluir este produto?")) {
			await deleteProduct.mutateAsync(productId);
		}
	};

	const handleUpdateStatus = async (productId: string, status: Product["status"]) => {
		await updateStatus.mutateAsync({ id: productId, status });
	};

	const handleFilterChange = (key: string, value: string | undefined) => {
		const newFilters = {
			...filters,
			[key]: value
		};
		setFilters(newFilters);
		setPage(1); // Reset to first page when filters change
		updateURL(search, newFilters, 1);
	};

	const handleClearFilters = () => {
		setFilters({});
		setPage(1); // Reset to first page when clearing filters
		updateURL("", {}, 1);
	};

	const handleSearchChange = (newSearch: string) => {
		setSearch(newSearch);
		setPage(1); // Reset to first page when search changes
		updateURL(newSearch, filters, 1);
	};

	const handlePageChange = (newPage: number) => {
		setPage(newPage);
		updateURL(search, filters, newPage);
	};

	const handleExport = () => {
		// Create CSV data
		const csvData = [
			['Nome', 'Tipo', 'Status', 'Preço', 'Alunos', 'Vendas', 'Receita', 'Criado em', 'Atualizado em'],
			...products.map(product => [
				product.name,
				getTypeLabel(product.type),
				getStatusLabel(product.status),
				formatCurrency(product.priceCents, product.currency),
				product._count?.enrollments || 0,
				product._count?.orders || 0,
				formatCurrency((product._count?.orders || 0) * product.priceCents, product.currency),
				new Date(product.createdAt).toLocaleDateString('pt-BR'),
				new Date(product.updatedAt).toLocaleDateString('pt-BR')
			])
		];

		// Convert to CSV string
		const csvString = csvData.map(row =>
			row.map(field => `"${field}"`).join(',')
		).join('\n');

		// Create and download file
		const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
		const link = document.createElement('a');
		const url = URL.createObjectURL(blob);
		link.setAttribute('href', url);
		link.setAttribute('download', `produtos-${new Date().toISOString().split('T')[0]}.csv`);
		link.style.visibility = 'hidden';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	// Componente de filtros avançado
	const AdvancedFilters = () => {
		const getActiveFiltersCount = () => {
			let count = 0;
			if (search) count++;
			if (filters.status) count++;
			if (filters.type) count++;
			return count;
		};

		const activeFiltersCount = getActiveFiltersCount();

		return (
			<div className="space-y-4">
				{/* Search and Quick Actions */}
				<div className="flex items-center justify-between gap-4">
					<div className="flex items-center gap-2 flex-1">
						{/* Search Input */}
						<div className="relative flex-1 max-w-md">
							<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
							<Input
								placeholder="Buscar produtos..."
								value={search}
								onChange={(e) => handleSearchChange(e.target.value)}
								className="pl-10"
							/>
						</div>

						{/* Status Filter */}
						<Select
							value={filters.status || "all"}
							onValueChange={(value) =>
								handleFilterChange('status', value === "all" ? undefined : value)
							}
						>
							<SelectTrigger className="w-[180px]">
								<SelectValue placeholder="Todos os status" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Todos os status</SelectItem>
								<SelectItem value="PUBLISHED">Publicado</SelectItem>
								<SelectItem value="DRAFT">Rascunho</SelectItem>
								<SelectItem value="ARCHIVED">Arquivado</SelectItem>
								<SelectItem value="SUSPENDED">Suspenso</SelectItem>
							</SelectContent>
						</Select>

						{/* Type Filter */}
						<Select
							value={filters.type || "all"}
							onValueChange={(value) =>
								handleFilterChange('type', value === "all" ? undefined : value)
							}
						>
							<SelectTrigger className="w-[180px]">
								<SelectValue placeholder="Todos os tipos" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Todos os tipos</SelectItem>
								<SelectItem value="COURSE">Curso</SelectItem>
								<SelectItem value="EBOOK">E-book</SelectItem>
								<SelectItem value="MENTORING">Mentoria</SelectItem>
								<SelectItem value="SUBSCRIPTION">Assinatura</SelectItem>
								<SelectItem value="BUNDLE">Pacote</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{/* Clear Filters */}
					{activeFiltersCount > 0 && (
						<Button
							variant="outline"
							size="sm"
							onClick={handleClearFilters}
							className="flex items-center gap-2"
						>
							<XIcon className="h-4 w-4" />
							Limpar Filtros ({activeFiltersCount})
						</Button>
					)}
				</div>

				{/* Active Filters Display */}
				{activeFiltersCount > 0 && (
					<div className="flex flex-wrap gap-2">
						{search && (
							<div className="flex items-center gap-1 px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs">
								Busca: {search}
								<XIcon
									className="h-3 w-3 cursor-pointer"
									onClick={() => handleSearchChange("")}
								/>
							</div>
						)}
						{filters.status && (
							<div className="flex items-center gap-1 px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs">
								Status: {getStatusLabel(filters.status)}
								<XIcon
									className="h-3 w-3 cursor-pointer"
									onClick={() => handleFilterChange('status', undefined)}
								/>
							</div>
						)}
						{filters.type && (
							<div className="flex items-center gap-1 px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs">
								Tipo: {getTypeLabel(filters.type)}
								<XIcon
									className="h-3 w-3 cursor-pointer"
									onClick={() => handleFilterChange('type', undefined)}
								/>
							</div>
						)}
					</div>
				)}
			</div>
		);
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<PageHeader
					title="Meus Produtos"
					subtitle="Gerencie seus produtos digitais e acompanhe as vendas"
					actions={
						<>
							<Button variant="outline" disabled>
								<DownloadIcon className="h-4 w-4 mr-2" />
								Exportar
							</Button>
							<SimpleCreateProductSheet
								organizationId={organizationId}
								organizationSlug={organizationSlug}
							/>
						</>
					}
				/>

				{/* Filtros e Exportar Skeleton */}
				<div className="flex items-center justify-between gap-4">
					<div className="flex-1">
						<div className="flex items-center gap-4">
							<div className="flex-1 max-w-md h-10 bg-muted/50 rounded-md animate-pulse"></div>
							<div className="w-[180px] h-10 bg-muted/50 rounded-md animate-pulse"></div>
							<div className="w-[180px] h-10 bg-muted/50 rounded-md animate-pulse"></div>
						</div>
					</div>
					<div className="flex items-center gap-2">
						<div className="w-[120px] h-8 bg-muted/50 rounded-md animate-pulse"></div>
						<div className="w-[100px] h-8 bg-muted/50 rounded-md animate-pulse"></div>
					</div>
				</div>

				<div className="space-y-6">
					{/* Stats Cards Skeleton */}
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
						{[...Array(4)].map((_, i) => (
							<Card key={i} className="relative overflow-hidden shadow-sm border-border/50">
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
									<div className="h-4 bg-muted/50 rounded w-3/4 animate-pulse"></div>
									<div className="h-8 w-8 bg-muted/50 rounded-full animate-pulse"></div>
								</CardHeader>
								<CardContent>
									<div className="h-8 bg-muted/50 rounded w-1/2 animate-pulse mb-2"></div>
									<div className="flex items-center gap-2">
										<div className="h-3 w-3 bg-muted/50 rounded animate-pulse"></div>
										<div className="h-3 bg-muted/50 rounded w-2/3 animate-pulse"></div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>

					{/* Products Skeleton - Novo Layout */}
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
						{[...Array(6)].map((_, i) => (
							<Card key={i} className="border-border/50 overflow-hidden">
								<div className="flex h-28">
									{/* Image Skeleton */}
									<div className="w-24 h-full bg-muted/50 animate-pulse"></div>

									{/* Content Skeleton */}
									<div className="flex-1 p-4 flex flex-col justify-between">
										<div className="space-y-2">
											<div className="h-4 bg-muted/50 rounded w-3/4 animate-pulse"></div>
											<div className="h-3 bg-muted/50 rounded w-1/2 animate-pulse"></div>
											<div className="h-5 bg-muted/50 rounded-full w-16 animate-pulse"></div>
										</div>
									</div>
								</div>
							</Card>
						))}
					</div>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="space-y-6">
				<PageHeader
					title="Meus Produtos"
					subtitle="Gerencie seus produtos digitais e acompanhe as vendas"
					actions={
						<>
							<Button variant="outline" disabled>
								<DownloadIcon className="h-4 w-4 mr-2" />
								Exportar
							</Button>
							<SimpleCreateProductSheet
								organizationId={organizationId}
								organizationSlug={organizationSlug}
							/>
						</>
					}
				/>
				<Card>
					<CardContent className="flex flex-col items-center justify-center py-12">
						<p className="text-red-500">Erro ao carregar produtos</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Calcular estatísticas
	const totalProdutos = pagination?.total || 0;
	const totalEnrollments = products.reduce((acc, product) => acc + (product._count?.enrollments || 0), 0);
	const totalSales = products.reduce((acc, product) => acc + (product._count?.orders || 0), 0);
	const totalRevenue = products.reduce((acc, product) => {
		// Aqui você precisaria buscar os dados de receita de cada produto
		// Por enquanto, vamos usar um valor estimado baseado nas vendas
		return acc + ((product._count?.orders || 0) * product.priceCents);
	}, 0);



	return (
		<div className="space-y-6">
			<PageHeader
				title="Meus Produtos"
				subtitle="Gerencie seus produtos digitais e acompanhe as vendas"
				actions={
					<SimpleCreateProductSheet
						organizationId={organizationId}
						organizationSlug={organizationSlug}
					/>
				}
			/>
			<div className="space-y-6">
				{/* Enhanced Stats Cards */}
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<MetricCard
						title="Total de Produtos"
						value={totalProdutos}
						subtitle="produtos cadastrados"
						icon={ImageIcon}
						trend="up"
						trendValue="+12%"
						className="bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20"
					/>

					<MetricCard
						title="Alunos Inscritos"
						value={totalEnrollments}
						subtitle="alunos matriculados"
						icon={UsersIcon}
						trend="up"
						trendValue="+8%"
						className="bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20"
					/>

					<MetricCard
						title="Total de Vendas"
						value={totalSales}
						subtitle="vendas realizadas"
						icon={DollarSignIcon}
						trend="up"
						trendValue="+15%"
						className="bg-gradient-to-br from-purple-50/50 to-transparent dark:from-purple-950/20"
					/>

					<MetricCard
						title="Receita Total"
						value={formatCurrency(totalRevenue)}
						subtitle="receita acumulada"
						icon={DollarSignIcon}
						trend="up"
						trendValue="+22%"
						className="bg-gradient-to-br from-orange-50/50 to-transparent dark:from-orange-950/20"
					/>
				</div>

				{/* Filtros e Exportar */}
				<div className="flex items-center justify-between gap-4">
					{/* Advanced Filters */}
					<div className="flex-1">
						<AdvancedFilters />
					</div>

					{/* Botão Exportar */}
					<div className="flex items-center gap-2">
						{Object.values(filters).some(value => value !== undefined) && (
							<Button variant="outline" onClick={handleClearFilters}>
								<XIcon className="h-4 w-4 mr-2" />
								Limpar Filtros
							</Button>
						)}
						<Button variant="outline" onClick={handleExport}>
							<DownloadIcon className="h-4 w-4 mr-2" />
							Exportar
						</Button>
					</div>
				</div>

			{/* Produtos Grid - Design Simplificado */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				{products.map((product) => {
					const TypeIcon = getTypeIcon(product.type);
					return (
						<Link key={product.id} href={`/app/${organizationSlug}/products/${product.id}`}>
							<Card className="group hover:shadow-md transition-all duration-200 border-border/50 overflow-hidden cursor-pointer">
								<div className="flex h-28">
									{/* Product Image/Icon - Lado Esquerdo - Altura Total */}
									<div className="w-24 h-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 flex items-center justify-center flex-shrink-0">
										{product.thumbnail ? (
											<img
												src={product.thumbnail}
												alt={product.name}
												className="w-full h-full object-cover"
											/>
										) : (
											<TypeIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
										)}
									</div>

									{/* Product Info - Lado Direito */}
									<div className="flex-1 p-4 flex flex-col justify-between min-h-0">
										<div className="flex-1 flex flex-col justify-between">
											<div>
												{/* Título e Subtítulo */}
												<h3 className="font-semibold text-foreground text-base mb-1 truncate group-hover:text-primary transition-colors" title={product.name}>
													{product.name}
												</h3>
												<p className="text-sm text-muted-foreground mb-2 truncate">
													{getTypeLabel(product.type)}
												</p>
											</div>

											{/* Badge de Status */}
											<div className="flex gap-2 mb-2">
												<div className={`px-2 py-1 rounded-full text-xs font-medium ${
													product.status === 'PUBLISHED' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
													product.status === 'DRAFT' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
													product.status === 'ARCHIVED' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400' :
													'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
												}`}>
													{getStatusLabel(product.status)}
												</div>
											</div>
										</div>

									</div>
								</div>
							</Card>
						</Link>
					);
				})}
			</div>

			{/* Enhanced Pagination */}
			{pagination && pagination.pages > 1 && (
				<div className="flex items-center justify-center space-x-2">
					<Button
						variant="outline"
						onClick={() => handlePageChange(page - 1)}
						disabled={page === 1}
						className="hover:bg-primary/10"
					>
						Previous
					</Button>
					<div className="flex items-center space-x-1">
						{Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
							const pageNum = i + 1;
							return (
								<Button
									key={pageNum}
									variant={page === pageNum ? "primary" : "outline"}
									size="sm"
									onClick={() => handlePageChange(pageNum)}
									className={page === pageNum ? "" : "hover:bg-primary/10"}
								>
									{pageNum}
								</Button>
							);
						})}
						{pagination.pages > 5 && (
							<>
								<span className="text-muted-foreground">...</span>
								<Button
									variant={page === pagination.pages ? "primary" : "outline"}
									size="sm"
									onClick={() => handlePageChange(pagination.pages)}
									className={page === pagination.pages ? "" : "hover:bg-primary/10"}
								>
									{pagination.pages}
								</Button>
							</>
						)}
					</div>
					<Button
						variant="outline"
						onClick={() => handlePageChange(page + 1)}
						disabled={page === pagination.pages}
						className="hover:bg-primary/10"
					>
						Next
					</Button>
				</div>
			)}

			{products.length === 0 && !isLoading && (
				<Card className="shadow-sm border-border/50 bg-gradient-to-br from-muted/20 to-transparent">
					<CardContent className="flex flex-col items-center justify-center py-20">
						<div className="relative mb-8">
							<div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mb-4">
								<ImageIcon className="h-10 w-10 text-primary/60" />
							</div>
							<div className="absolute -top-1 -right-1 w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
								<PlusIcon className="h-3 w-3 text-primary" />
							</div>
						</div>
						<h3 className="text-2xl font-bold mb-3 text-foreground">
							Nenhum produto encontrado
						</h3>
						<p className="text-muted-foreground text-center mb-8 max-w-md leading-relaxed">
							Crie seu primeiro produto digital e comece a vender online. É rápido e fácil começar!
						</p>
						<div className="flex flex-col sm:flex-row gap-3">
							<SimpleCreateProductSheet
								organizationId={organizationId}
								organizationSlug={organizationSlug}
								trigger={
									<Button size="lg" className="shadow-sm hover:shadow-md transition-shadow">
										<PlusIcon className="h-4 w-4 mr-2" />
										Criar Primeiro Produto
									</Button>
								}
							/>
							<Button variant="outline" size="lg" asChild>
								<Link href="/help/getting-started">
									<BookOpenIcon className="h-4 w-4 mr-2" />
									Learn More
								</Link>
							</Button>
						</div>
					</CardContent>
				</Card>
			)}

			{error && (
				<Card className="shadow-sm border-destructive/50 bg-gradient-to-br from-destructive/5 to-transparent">
					<CardContent className="flex flex-col items-center justify-center py-16">
						<div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mb-6">
							<ImageIcon className="h-8 w-8 text-destructive" />
						</div>
						<h3 className="text-xl font-semibold mb-2 text-destructive">
							Erro ao carregar produtos
						</h3>
						<p className="text-muted-foreground text-center mb-6 max-w-md">
							Ocorreu um erro ao carregar seus produtos. Tente novamente.
						</p>
						<Button
							variant="outline"
							onClick={() => window.location.reload()}
							className="border-destructive/50 text-destructive hover:bg-destructive/10"
						>
							Try Again
						</Button>
					</CardContent>
				</Card>
			)}

			</div>
		</div>
	);
}
