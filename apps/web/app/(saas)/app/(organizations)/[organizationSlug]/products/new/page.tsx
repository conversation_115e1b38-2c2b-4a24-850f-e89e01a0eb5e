import { getSession } from "@saas/auth/lib/server";
import { isAdmin } from "@repo/auth/lib/helper";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon } from "lucide-react";
import Link from "next/link";
import { CreateProductForm } from "./components/CreateProductForm";

interface NewProductPageProps {
  params: Promise<{ organizationSlug: string }>;
}

export default async function NewProductPage({ params }: NewProductPageProps) {
  const { organizationSlug } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  let organization;

  // Verificar se é admin - admins podem acessar sem membership
  if (isAdmin(session.user)) {
    // Para admins, buscar a primeira organização disponível
    organization = await db.organization.findFirst();

    if (!organization) {
      redirect("/auth/login");
    }
  } else {
    // Buscar organização do usuário para usuários normais
    const userMembership = await db.member.findFirst({
      where: {
        userId: session.user.id,
        organization: {
          slug: organizationSlug,
        },
      },
      include: {
        organization: true,
      },
    });

    if (!userMembership) {
      redirect("/auth/login");
    }

    organization = userMembership.organization;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Criar Novo Produto"
        subtitle="Preencha as informações básicas para criar seu produto. Após a criação, você poderá configurar detalhes avançados."
        actions={
          <Button variant="outline" asChild>
            <Link href={`/app/${organizationSlug}/products`}>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Voltar para Produtos
            </Link>
          </Button>
        }
      />

      <CreateProductForm organization={organization} organizationSlug={organizationSlug} />
    </div>
  );
}
