import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { CheckoutsPageClient } from "./components/CheckoutsPageClient";
import { ProductPageHeader } from "../components/ProductPageHeader";
import { Button } from "@ui/components/button";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function CheckoutsPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <ProductPageHeader
        productName={product.name}
        currentPage="Checkouts"
        productStatus={product.status}
        productType={product.type}
        productId={product.id}
      />

      {/* Main Content */}
      <CheckoutsPageClient
        organizationSlug={organizationSlug}
        productId={productId}
        product={product}
        organizationId={product.organizationId}
      />
    </div>
  );
}
