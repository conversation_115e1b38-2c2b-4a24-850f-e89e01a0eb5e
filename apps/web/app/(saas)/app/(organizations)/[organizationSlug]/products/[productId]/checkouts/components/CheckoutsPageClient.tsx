"use client";

import { useState } from "react";
import { useCheckouts } from "@saas/products/hooks/useCheckouts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import {
  PlusIcon,
  CreditCardIcon,
  MoreHorizontalIcon,
  ExternalLinkIcon,
  EditIcon,
  TrashIcon,
  CopyIcon,
  EyeIcon
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import { CreateCheckoutModal } from "./CreateCheckoutModal";

interface CheckoutsPageClientProps {
  organizationSlug: string;
  productId: string;
  product: any;
  organizationId: string;
}



const paymentMethodLabels: Record<string, string> = {
  credit_card: "Cartão",
  pix: "PIX",
  boleto: "Boleto",
};

const layoutLabels: Record<string, string> = {
  default: "Padrão",
  minimal: "Minimalista",
  modern: "Moderno",
};

// Componente Badge simples
function Badge({
  children,
  variant = "default",
  className
}: {
  children: React.ReactNode;
  variant?: "default" | "secondary" | "outline";
  className?: string;
}) {
  const baseClasses = "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium";
  const variantClasses = {
    default: "bg-primary text-primary-foreground",
    secondary: "bg-secondary text-secondary-foreground",
    outline: "border border-input bg-background text-foreground",
  };

  return (
    <span className={cn(baseClasses, variantClasses[variant], className)}>
      {children}
    </span>
  );
}

export function CheckoutsPageClient({
  organizationSlug,
  productId,
  product,
  organizationId,
}: CheckoutsPageClientProps) {
  const { checkouts, deleteCheckout, copyCheckoutUrl, generateQuickCheckout } = useCheckouts(productId, organizationId);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Checkouts</h1>
              <p className="text-muted-foreground">Configure e gerencie seus checkouts de pagamento</p>
            </div>
            <div className="flex gap-2">
              <Button onClick={() => generateQuickCheckout()}>
                Checkout Rápido
              </Button>
              <CreateCheckoutModal
                organizationSlug={organizationSlug}
                productId={productId}
                product={product}
                organizationId={organizationId}
                onCheckoutCreated={handleRefresh}
              />
            </div>
          </div>

          {/* Checkout Padrão */}
          <Card>
            <CardHeader>
              <CardTitle>Checkout Padrão</CardTitle>
              <CardDescription>
                Configurações básicas do checkout para este produto
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Informações do Checkout */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Nome do Checkout</label>
                    <p className="text-sm text-muted-foreground mt-1">Checkout Padrão - {product.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Status</label>
                    <p className="text-sm text-green-600 mt-1">✓ Ativo</p>
                  </div>
                </div>

                {/* Elementos de Conversão */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Elementos de Conversão</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <label className="text-sm font-medium">Depoimentos</label>
                        <p className="text-xs text-muted-foreground">Exibir depoimentos de clientes</p>
                      </div>
                      <div className="w-10 h-6 bg-primary rounded-full flex items-center justify-end px-1">
                        <div className="w-4 h-4 bg-white rounded-full"></div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <label className="text-sm font-medium">Urgência</label>
                        <p className="text-xs text-muted-foreground">Exibir elementos de urgência</p>
                      </div>
                      <div className="w-10 h-6 bg-primary rounded-full flex items-center justify-end px-1">
                        <div className="w-4 h-4 bg-white rounded-full"></div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <label className="text-sm font-medium">Escassez</label>
                        <p className="text-xs text-muted-foreground">Exibir elementos de escassez</p>
                      </div>
                      <div className="w-10 h-6 bg-primary rounded-full flex items-center justify-end px-1">
                        <div className="w-4 h-4 bg-white rounded-full"></div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <label className="text-sm font-medium">Badges de Confiança</label>
                        <p className="text-xs text-muted-foreground">Exibir selos de segurança</p>
                      </div>
                      <div className="w-10 h-6 bg-primary rounded-full flex items-center justify-end px-1">
                        <div className="w-4 h-4 bg-white rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Ações */}
                <div className="flex gap-2 pt-4 border-t">
                  <Button variant="outline" size="sm">
                    <EditIcon className="h-4 w-4 mr-2" />
                    Editar Configurações
                  </Button>
                  <Button variant="outline" size="sm">
                    <EyeIcon className="h-4 w-4 mr-2" />
                    Visualizar Checkout
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Checkouts Adicionais */}
          <Card>
            <CardHeader>
              <CardTitle>Checkouts Adicionais</CardTitle>
              <CardDescription>
                Crie checkouts personalizados com diferentes configurações
              </CardDescription>
            </CardHeader>
            <CardContent>
              {checkouts.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-12 h-12 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-3">
                    <CreditCardIcon className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="text-sm font-semibold mb-2">Nenhum checkout adicional</h3>
                  <p className="text-xs text-muted-foreground mb-4">
                    Crie checkouts personalizados para diferentes campanhas
                  </p>
                  <div className="flex justify-center">
                    <CreateCheckoutModal
                      organizationSlug={organizationSlug}
                      productId={productId}
                      product={product}
                      organizationId={organizationId}
                      variant="outline"
                      buttonText="Criar Checkout Personalizado"
                      onCheckoutCreated={handleRefresh}
                    />
                  </div>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Layout</TableHead>
                      <TableHead>Métodos de Pagamento</TableHead>
                      <TableHead>Provedor</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Criado em</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {checkouts.map((checkout) => (
                      <TableRow key={checkout.id}>
                        <TableCell className="font-medium">
                          {checkout.name}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {layoutLabels[checkout.layout]}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1 flex-wrap">
                            {checkout.paymentMethods.map((method) => (
                              <Badge key={method} variant="secondary" className="text-xs">
                                {paymentMethodLabels[method]}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            Stripe
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge variant={checkout.isActive ? "default" : "secondary"}>
                            {checkout.isActive ? "Ativo" : "Inativo"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDate(checkout.createdAt)}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontalIcon className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <EyeIcon className="h-4 w-4 mr-2" />
                                Visualizar
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <EditIcon className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => copyCheckoutUrl(checkout.url)}>
                                <CopyIcon className="h-4 w-4 mr-2" />
                                Copiar URL
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => window.open(checkout.url, '_blank')}>
                                <ExternalLinkIcon className="h-4 w-4 mr-2" />
                                Abrir Checkout
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => deleteCheckout(checkout.id)}
                              >
                                <TrashIcon className="h-4 w-4 mr-2" />
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
