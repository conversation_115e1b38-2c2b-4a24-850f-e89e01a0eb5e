import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { PixelsPageClient } from "./components/PixelsPageClient";
import { ProductPageHeader } from "../components/ProductPageHeader";
import { Button } from "@ui/components/button";
import { PlusIcon } from "lucide-react";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function TrackingPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <ProductPageHeader
        productName={product.name}
        currentPage="Pixels de Rastreamento"
        productStatus={product.status}
        productType={product.type}
        productId={product.id}
      >
        <Button>
          <PlusIcon className="h-4 w-4 mr-2" />
          Novo Pixel
        </Button>
      </ProductPageHeader>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Pixels de Rastreamento</h1>
            <p className="text-muted-foreground">Configure pixels do Google Analytics e Facebook para rastrear conversões</p>
          </div>

          <PixelsPageClient
            organizationSlug={organizationSlug}
            productId={productId}
            product={product}
          />
        </div>
      </div>
    </div>
  );
}
