import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { ProductConfigurationClient } from "./settings/components/ProductConfigurationClient";
import { ProductPageHeader } from "./components/ProductPageHeader";

interface ProductPageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
      _count: {
        select: {
          orders: true,
          enrollments: true,
        },
      },
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <ProductPageHeader
        productName={product.name}
        currentPage="Informações Básicas"
        productStatus={product.status}
        productType={product.type}
        productId={product.id}
      />

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <ProductConfigurationClient
          product={product}
          organizationSlug={organizationSlug}
        />
      </div>
    </div>
  );
}
