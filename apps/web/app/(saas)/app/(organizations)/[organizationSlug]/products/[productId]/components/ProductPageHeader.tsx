"use client";

import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { ExternalLinkIcon } from "lucide-react";
import { toast } from "sonner";

interface ProductPageHeaderProps {
  productName: string;
  currentPage: string;
  productStatus?: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED' | 'SUSPENDED';
  productType?: string;
  productId?: string;
  children?: React.ReactNode;
}

export function ProductPageHeader({
  productName,
  currentPage,
  productStatus,
  productType,
  productId,
  children
}: ProductPageHeaderProps) {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'PUBLISHED': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'DRAFT': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'ARCHIVED': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'SUSPENDED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeLabel = (type?: string) => {
    switch (type) {
      case 'COURSE': return 'Curso';
      case 'EBOOK': return 'E-book';
      case 'MENTORSHIP': return 'Mentoria';
      case 'SUBSCRIPTION': return 'Assinatura';
      case 'BUNDLE': return 'Pacote';
      case 'SERVICE': return 'Serviço';
      default: return type || 'Produto';
    }
  };

  const handleCheckoutLink = () => {
    if (!productId) {
      toast.error('ID do produto não encontrado');
      return;
    }

    // Gerar URL do checkout
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const checkoutUrl = `${baseUrl}/checkout/${productId}`;

    // Abrir em nova aba
    window.open(checkoutUrl, '_blank');
    toast.success('Link do checkout aberto em nova aba!');
  };

  return (
    <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Breadcrumbs */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Produtos</span>
          <span>/</span>
          <span className="text-foreground font-medium">{currentPage}</span>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2">
          {productStatus === 'PUBLISHED' && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleCheckoutLink}
            >
              <ExternalLinkIcon className="h-4 w-4 mr-2" />
              Link do Checkout
            </Button>
          )}
          {children}
        </div>
      </div>
    </div>
  );
}
