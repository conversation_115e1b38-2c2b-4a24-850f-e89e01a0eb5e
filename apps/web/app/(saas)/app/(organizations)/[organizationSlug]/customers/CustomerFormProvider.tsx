"use client";

import { CustomerFormProvider as Provider, useCustomerForm } from "@saas/customers/hooks/useCustomerForm";
import { SimpleCustomerModal } from "@saas/customers/components/SimpleCustomerModal";
import { useCreateCustomer } from "@saas/customers/hooks/useCustomers";

interface CustomerFormProviderProps {
  children: React.ReactNode;
  organizationId: string;
}

function CustomerFormContent({ organizationId }: { organizationId: string }) {
  const { isFormOpen, selectedCustomer, mode, closeForm } = useCustomerForm();
  const { mutate: createCustomer, isPending: isCreating } = useCreateCustomer();

  const handleSave = async (customerData: any) => {
    console.log('handleSave called with:', { mode, customerData });

    if (mode === 'create') {
      createCustomer({
        organizationId: organizationId,
        name: customerData.name,
        email: customerData.email,
        phone: customerData.phone || '',
        city: customerData.city || '',
        state: customerData.state || '',
        document: customerData.document || '',
        birthDate: customerData.birthDate || '',
        address: customerData.address || '',
        zipCode: customerData.zipCode || '',
        locale: customerData.locale || 'pt-BR',
      }, {
        onSuccess: (data) => {
          console.log('Customer created successfully:', data);
          closeForm();
        },
        onError: (error) => {
          console.error('Error saving customer:', error);
          alert(`Erro ao criar cliente: ${error.message}`);
        }
      });
    } else {
      // TODO: Implementar edição
      console.log('Edit customer:', customerData);
      closeForm();
    }
  };

  return (
    <SimpleCustomerModal
      isOpen={isFormOpen}
      onClose={closeForm}
      customer={selectedCustomer}
      onSave={handleSave}
      mode={mode}
      organizationId={organizationId}
    />
  );
}

export function CustomerFormProvider({ children, organizationId }: CustomerFormProviderProps) {
  return (
    <Provider>
      {children}
      <CustomerFormContent organizationId={organizationId} />
    </Provider>
  );
}
