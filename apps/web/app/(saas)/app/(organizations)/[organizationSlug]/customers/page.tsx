import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import ClientPage from "./ClientPage";

export default async function CustomersPage({
  params,
  searchParams,
}: {
  params: Promise<{ organizationSlug: string }>;
  searchParams: Promise<{
    search?: string;
    status?: string[] | string;
    location?: string;
    join_date_from?: string;
    join_date_to?: string;
    total_spent_min?: string;
    total_spent_max?: string;
  }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  const resolvedSearchParams = await searchParams;

  // Parse filter parameters
  const searchTerm = resolvedSearchParams.search;
  const statuses = resolvedSearchParams.status
    ? Array.isArray(resolvedSearchParams.status)
      ? resolvedSearchParams.status
      : [resolvedSearchParams.status]
    : undefined;
  const location = resolvedSearchParams.location;

  return (
    <ClientPage
      organization={organization}
      initialFilters={{
        searchTerm,
        status: statuses,
        location,
      }}
    />
  );
}
