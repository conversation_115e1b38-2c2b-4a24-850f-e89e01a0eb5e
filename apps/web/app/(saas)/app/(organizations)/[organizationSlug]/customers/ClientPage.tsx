"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { CustomerMetrics } from "@saas/customers/components/CustomerMetrics";
import { CustomerFilters, CustomerFilterState } from "@saas/customers/components/CustomerFilters";
import { CustomersTable } from "@saas/customers/components/CustomersTable";
import { CustomerFormProvider } from "./CustomerFormProvider";
import { useCustomerForm } from "@saas/customers/hooks/useCustomerForm";
import { Button } from "@ui/components/button";
import { PlusIcon } from "lucide-react";

interface ClientPageProps {
  organization: {
    id: string;
    slug: string;
    name: string;
    createdAt: Date;
  };
  initialFilters: {
    searchTerm?: string;
    status?: string[];
    location?: string;
  };
}

function CustomerPageContent({ organization, initialFilters }: ClientPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { openCreateForm } = useCustomerForm();

  const [filters, setFilters] = useState<CustomerFilterState>({
    searchTerm: initialFilters.searchTerm || "",
    status: initialFilters.status || [],
    totalSpentRange: { min: null, max: null },
    location: initialFilters.location || "",
    joinDateRange: { from: undefined, to: undefined },
  });

  // Update URL when filters change
  const updateURL = (newFilters: CustomerFilterState) => {
    const params = new URLSearchParams();

    // Add filters
    if (newFilters.searchTerm) params.set('search', newFilters.searchTerm);
    if (newFilters.location) params.set('location', newFilters.location);
    newFilters.status.forEach(status => params.append('status', status));

    // Add date range
    if (newFilters.joinDateRange.from) params.set('join_date_from', newFilters.joinDateRange.from.toISOString());
    if (newFilters.joinDateRange.to) params.set('join_date_to', newFilters.joinDateRange.to.toISOString());

    // Add amount range
    if (newFilters.totalSpentRange.min !== null) params.set('total_spent_min', newFilters.totalSpentRange.min.toString());
    if (newFilters.totalSpentRange.max !== null) params.set('total_spent_max', newFilters.totalSpentRange.max.toString());

    const url = `/app/${organization.slug}/customers${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(url, { scroll: false });
  };

  const handleFiltersChange = (newFilters: CustomerFilterState) => {
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const handleExport = () => {
    // Implement export functionality
    console.log("Exporting customers data...");
  };


  return (
    <div className="space-y-6">
      <PageHeader
        title="Clientes"
        subtitle="Gerencie seus clientes e acompanhe métricas importantes"
        actions={
          <Button variant="primary" onClick={openCreateForm}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Novo Cliente
          </Button>
        }
      />

      {/* Métricas e Filtros */}
      <div className="space-y-6">
        {/* Métricas */}
        <CustomerMetrics organizationId={organization.id} />

        {/* Filtros */}
        <CustomerFilters
          organizationId={organization.id}
          onFiltersChange={handleFiltersChange}
          onExport={handleExport}
        />

        {/* Tabela de Clientes */}
        <CustomersTable
        organizationId={organization.id}
        filters={filters}
      />
      </div>
    </div>
  );
}

const ClientPage: React.FC<ClientPageProps> = ({
  organization,
  initialFilters,
}) => {
  return (
    <CustomerFormProvider organizationId={organization.id}>
      <CustomerPageContent organization={organization} initialFilters={initialFilters} />
    </CustomerFormProvider>
  );
};

export default ClientPage;
