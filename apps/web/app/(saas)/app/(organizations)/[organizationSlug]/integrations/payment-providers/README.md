# Adquirentes de Pagamento

Esta página permite gerenciar integrações com diferentes provedores de pagamento (adquirentes) para processar transações.

## Funcionalidades

- **Listagem de Providers**: Visualize todos os provedores de pagamento disponíveis
- **Filtros**: Filtre por categoria, status de instalação, ou providers novos
- **Instalação/Desinstalação**: Gerencie quais providers estão ativos
- **Informações Detalhadas**: Veja recursos, disponibilidade e estatísticas de cada provider

## Providers Disponíveis

### Instantâneos
- **PIX**: Pagamento instantâneo do Banco Central do Brasil
- **Mercado Pago**: Fintech da América Latina

### Infraestrutura
- **Pushin Pay**: Infraestrutura de pagamentos com alta disponibilidade
- **Efí Pay**: Nova infraestrutura de pagamentos

### Ecosistema
- **SuitPay**: Ecosistema completo para negócios digitais

### Global
- **Stripe**: Plataforma global de pagamentos

## Estrutura de Arquivos

```
payment-providers/
├── page.tsx                           # Página principal
├── components/
│   ├── PaymentProvidersClient.tsx     # Cliente principal com lógica
│   ├── PaymentProviderCard.tsx        # Card individual do provider
│   └── types.ts                       # Tipos TypeScript
└── README.md                          # Esta documentação
```

## Como Usar

1. Acesse a página de integrações
2. Clique no card "Adquirentes de Pagamento"
3. Navegue pelos providers disponíveis
4. Use os filtros para encontrar providers específicos
5. Clique em "Instalar" para ativar um provider
6. Clique em "Desinstalar" para desativar um provider

## Customização

Para adicionar novos providers, edite o array `mockProviders` em `PaymentProvidersClient.tsx`:

```typescript
const mockProviders: PaymentProvider[] = [
  {
    id: "novo-provider",
    name: "Novo Provider",
    description: "Descrição do novo provider",
    logo: "🏦",
    isNew: true,
    isInstalled: false,
    category: "instant",
    features: ["Feature 1", "Feature 2"],
    availability: "99.9%",
    transactionsPerDay: "100k+",
  },
  // ... outros providers
];
```
