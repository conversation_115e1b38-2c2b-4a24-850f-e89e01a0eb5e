"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { PageHeader } from "@saas/shared/components/PageHeader";
import {
  Search,
  Plus,
  CheckCircle,
  ExternalLink,
  Zap,
  CreditCard,
  Webhook,
  Bot,
  Database,
  Mail,
  BarChart3,
  Users,
  FileText,
  Settings,
  Lightbulb,
  RefreshCw,
  Download
} from "lucide-react";
import Link from "next/link";

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  isConnected: boolean;
  category: "payment" | "automation" | "membership" | "marketing" | "analytics" | "webhook" | "n8n";
  color: string;
  href?: string;
}

const mockIntegrations: Integration[] = [
  // Adquirentes de Pagamento
  {
    id: "payment-providers",
    name: "Adquirentes de Pagamento",
    description: "Conecte com PIX via Pluggou, Mercado Pago, Stripe e outros",
    icon: CreditCard,
    isConnected: false,
    category: "payment",
    color: "bg-blue-500",
    href: "/integrations/payment-providers"
  },

  // n8n - Automação
  {
    id: "n8n",
    name: "n8n",
    description: "Automatize workflows e conecte 400+ apps",
    icon: Zap,
    isConnected: false,
    category: "automation",
    color: "bg-orange-500"
  },

  // Webhooks
  {
    id: "webhooks",
    name: "Webhooks",
    description: "Integre com sistemas externos através de webhooks",
    icon: Webhook,
    isConnected: false,
    category: "webhook",
    color: "bg-purple-500"
  },

  // Cademi
  {
    id: "cademi",
    name: "Cademi",
    description: "Integre com sua área de membros",
    icon: Users,
    isConnected: true,
    category: "membership",
    color: "bg-red-500"
  },

  // Astron Members
  {
    id: "astron-members",
    name: "Astron Members",
    description: "Integre com sua área de membros",
    icon: Users,
    isConnected: false,
    category: "membership",
    color: "bg-indigo-500"
  },

  // Voxuy API
  {
    id: "voxuy-api",
    name: "Voxuy API",
    description: "Integre seu funil de vendas e mensagens",
    icon: Bot,
    isConnected: false,
    category: "marketing",
    color: "bg-cyan-500"
  },

  // Notazz
  {
    id: "notazz",
    name: "Notazz",
    description: "Integre com seu emissor de notas fiscais",
    icon: FileText,
    isConnected: false,
    category: "automation",
    color: "bg-teal-500"
  },

  // MemberKit
  {
    id: "memberkit",
    name: "MemberKit",
    description: "Integre com sua área de membros",
    icon: Users,
    isConnected: false,
    category: "membership",
    color: "bg-violet-500"
  },

  // eNotas
  {
    id: "enotas",
    name: "eNotas",
    description: "Integre com seu emissor de notas fiscais",
    icon: FileText,
    isConnected: false,
    category: "automation",
    color: "bg-orange-500"
  },

  // ActiveCampaign
  {
    id: "activecampaign",
    name: "ActiveCampaign",
    description: "Integre para criar fluxos automatizados",
    icon: Mail,
    isConnected: false,
    category: "marketing",
    color: "bg-blue-500"
  },

  // Analytics
  {
    id: "analytics",
    name: "Google Analytics",
    description: "Acompanhe métricas e conversões",
    icon: BarChart3,
    isConnected: false,
    category: "analytics",
    color: "bg-green-500"
  },

  // Database
  {
    id: "database",
    name: "Database Sync",
    description: "Sincronize dados entre sistemas",
    icon: Database,
    isConnected: false,
    category: "automation",
    color: "bg-gray-500"
  }
];

export function IntegrationsClient({
  organizationId,
  organizationSlug
}: {
  organizationId: string;
  organizationSlug: string;
}) {
  const [activeTab, setActiveTab] = useState<"all" | "connected" | "disconnected">("all");
  const [searchTerm, setSearchTerm] = useState("");

  const filteredIntegrations = mockIntegrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesTab = activeTab === "all" ||
                      (activeTab === "connected" && integration.isConnected) ||
                      (activeTab === "disconnected" && !integration.isConnected);

    return matchesSearch && matchesTab;
  });

  const connectedCount = mockIntegrations.filter(i => i.isConnected).length;
  const disconnectedCount = mockIntegrations.filter(i => !i.isConnected).length;

  return (
    <div className="space-y-6">
      <PageHeader
        title="Integrações"
        subtitle="Conecte sua organização com outras ferramentas e serviços"
        actions={
          <>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Configurações
            </Button>
            <Button className="bg-primary hover:bg-primary/90">
              <Lightbulb className="h-4 w-4 mr-2" />
              Sugerir um app
            </Button>
          </>
        }
      />

      {/* Tabs */}
      <div className="flex gap-1 border-b border-border">
        <Button
          variant="ghost"
          onClick={() => setActiveTab("all")}
          className={`rounded-none border-b-2 ${
            activeTab === "all"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          Todos ({mockIntegrations.length})
        </Button>
        <Button
          variant="ghost"
          onClick={() => setActiveTab("connected")}
          className={`rounded-none border-b-2 ${
            activeTab === "connected"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          Conectados ({connectedCount})
        </Button>
        <Button
          variant="ghost"
          onClick={() => setActiveTab("disconnected")}
          className={`rounded-none border-b-2 ${
            activeTab === "disconnected"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          Desconectados ({disconnectedCount})
        </Button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Buscar integrações..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Integrations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {filteredIntegrations.map((integration) => {
          const Icon = integration.icon;
          return (
            <Card
              key={integration.id}
              className="group hover:shadow-lg transition-all duration-200 cursor-pointer border-border/50 hover:border-primary/20"
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${integration.color} text-white`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-base font-semibold">
                        {integration.name}
                      </CardTitle>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {integration.isConnected ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-3">
                <CardDescription className="text-sm">
                  {integration.description}
                </CardDescription>

                <div className="flex items-center justify-between">
                  <Badge
                    variant={integration.isConnected ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {integration.isConnected ? "Conectado" : "Disponível"}
                  </Badge>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs h-8 px-2"
                    asChild
                  >
                    {integration.href ? (
                      <Link href={`/app/${organizationSlug}${integration.href}`}>
                        Ver integração
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Link>
                    ) : (
                      <span>
                        Ver integração
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </span>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredIntegrations.length === 0 && (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            Nenhuma integração encontrada para "{searchTerm}"
          </div>
          <Button
            variant="outline"
            onClick={() => setSearchTerm("")}
          >
            Limpar busca
          </Button>
        </div>
      )}
    </div>
  );
}
