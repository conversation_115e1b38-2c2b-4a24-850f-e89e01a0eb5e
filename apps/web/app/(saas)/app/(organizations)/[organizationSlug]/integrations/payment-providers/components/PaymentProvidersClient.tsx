"use client";

import { useState } from "react";
import { PaymentProviderCard } from "./PaymentProviderCard";
import { PaymentProvider } from "./types";

const mockProviders: PaymentProvider[] = [
  {
    id: "pluggou-pix",
    name: "PIX via Pluggou",
    description: "Sistema completo de PIX com múltiplos adquirentes, webhooks e relatórios avançados.",
    logo: "/images/gateways/pluggou.png",
    isNew: false,
    isInstalled: false,
    category: "pix",
    features: {
      pix: true,
      webhook: true,
      relatorios: true,
      antifraude: true,
    },
    highlight: true,
  },
  {
    id: "asaas",
    name: "ASAAS",
    description: "Plataforma de pagamentos com foco em PIX, cartões e boleto bancário.",
    logo: "/images/gateways/asaas.png",
    isNew: false,
    isInstalled: false,
    category: "fintech",
    features: {
      pix: true,
      cartao: true,
      boleto: true,
      webhook: true,
    },
  },
  {
    id: "mercado-pago",
    name: "<PERSON><PERSON><PERSON>",
    description: "Plataforma completa de pagamentos com suporte a PIX, cartões e boleto bancário.",
    logo: "/images/gateways/mercado-pago.png",
    isNew: false,
    isInstalled: true,
    category: "fintech",
    features: {
      pix: true,
      cartao: true,
      boleto: true,
      webhook: true,
    },
  },
  {
    id: "owempay",
    name: "Owempay",
    description: "Plataforma de pagamentos PIX com suporte completo a recebimentos e transferências.",
    logo: "/images/gateways/owempay.png",
    isNew: false,
    isInstalled: false,
    category: "pix",
    features: {
      pix: true,
      webhook: true,
      relatorios: true,
    },
  },
  {
    id: "pagarme",
    name: "Pagar.me",
    description: "Plataforma completa de pagamentos com suporte a PIX e diversos métodos de pagamento.",
    logo: "/images/gateways/pagarme.png",
    isNew: false,
    isInstalled: false,
    category: "fintech",
    features: {
      pix: true,
      cartao: true,
      boleto: true,
      webhook: true,
    },
  },
  {
    id: "transfeera",
    name: "Transfeera",
    description: "Solução completa para transferências e pagamentos via PIX com alta disponibilidade.",
    logo: "/images/gateways/transfeera.png",
    isNew: false,
    isInstalled: false,
    category: "pix",
    features: {
      pix: true,
      webhook: true,
      relatorios: true,
    },
  },
  {
    id: "zendry",
    name: "Zendry",
    description: "Plataforma de pagamentos PIX com QR codes dinâmicos e webhooks em tempo real.",
    logo: "/images/gateways/zendry.png",
    isNew: true,
    isInstalled: false,
    category: "pix",
    features: {
      pix: true,
      webhook: true,
      relatorios: true,
    },
  },
];

export function PaymentProvidersClient({ organizationId }: { organizationId: string }) {
  const [providers, setProviders] = useState<PaymentProvider[]>(mockProviders);
  const [filter, setFilter] = useState<string>("all");

  const filteredProviders = providers.filter(provider => {
    if (filter === "all") return true;
    if (filter === "installed") return provider.isInstalled;
    if (filter === "new") return provider.isNew;
    return provider.category === filter;
  });

  const handleInstall = (providerId: string) => {
    setProviders(prev =>
      prev.map(provider =>
        provider.id === providerId
          ? { ...provider, isInstalled: !provider.isInstalled }
          : provider
      )
    );
  };

  return (
    <div className="space-y-6">
      {/* Filtros */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => setFilter("all")}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === "all"
              ? "bg-primary text-primary-foreground"
              : "bg-muted text-muted-foreground hover:bg-muted/80"
          }`}
        >
          Todos
        </button>
        <button
          onClick={() => setFilter("installed")}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === "installed"
              ? "bg-primary text-primary-foreground"
              : "bg-muted text-muted-foreground hover:bg-muted/80"
          }`}
        >
          Instalados
        </button>
        <button
          onClick={() => setFilter("new")}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === "new"
              ? "bg-primary text-primary-foreground"
              : "bg-muted text-muted-foreground hover:bg-muted/80"
          }`}
        >
          Novos
        </button>
        <button
          onClick={() => setFilter("pix")}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === "pix"
              ? "bg-primary text-primary-foreground"
              : "bg-muted text-muted-foreground hover:bg-muted/80"
          }`}
        >
          PIX
        </button>
        <button
          onClick={() => setFilter("fintech")}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === "fintech"
              ? "bg-primary text-primary-foreground"
              : "bg-muted text-muted-foreground hover:bg-muted/80"
          }`}
        >
          Fintech
        </button>
        <button
          onClick={() => setFilter("global")}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === "global"
              ? "bg-primary text-primary-foreground"
              : "bg-muted text-muted-foreground hover:bg-muted/80"
          }`}
        >
          Global
        </button>
      </div>

      {/* Grid de Providers */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProviders.map((provider) => (
          <PaymentProviderCard
            key={provider.id}
            provider={provider}
            onInstall={handleInstall}
          />
        ))}
      </div>

      {filteredProviders.length === 0 && (
        <div className="text-center py-12">
          <div className="text-muted-foreground">
            Nenhum provedor encontrado para o filtro selecionado.
          </div>
        </div>
      )}
    </div>
  );
}
