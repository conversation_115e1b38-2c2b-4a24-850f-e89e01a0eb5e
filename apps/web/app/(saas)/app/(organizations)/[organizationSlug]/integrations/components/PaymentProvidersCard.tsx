"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { CreditCard, Zap, ExternalLink, ArrowRight } from "lucide-react";
import Link from "next/link";

interface PaymentProvidersCardProps {
  organizationSlug: string;
}

export function PaymentProvidersCard({ organizationSlug }: PaymentProvidersCardProps) {
  return (
    <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <CreditCard className="h-6 w-6 text-primary" />
            </div>
            <div>
              <CardTitle className="text-xl font-semibold">Adquirentes de Pagamento</CardTitle>
              <CardDescription className="text-base">
                Conecte-se com os principais provedores de pagamento do Brasil e do mundo
              </CardDescription>
            </div>
          </div>
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
            <Zap className="h-3 w-3 mr-1" />
            Essencial
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-sm text-foreground">Providers Disponíveis</h4>
            <div className="flex flex-wrap gap-1">
              <Badge variant="outline" className="text-xs">PIX</Badge>
              <Badge variant="outline" className="text-xs">Stripe</Badge>
              <Badge variant="outline" className="text-xs">Mercado Pago</Badge>
              <Badge variant="outline" className="text-xs">+3 mais</Badge>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm text-foreground">Categorias</h4>
            <div className="flex flex-wrap gap-1">
              <Badge variant="secondary" className="text-xs">Instantâneos</Badge>
              <Badge variant="secondary" className="text-xs">Infraestrutura</Badge>
              <Badge variant="secondary" className="text-xs">Global</Badge>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm text-foreground">Status</h4>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-success rounded-full"></div>
              <span className="text-xs text-muted-foreground">6 providers ativos</span>
            </div>
          </div>
        </div>

        <div className="pt-2">
          <Link href={`/app/organizations/${organizationSlug}/integrations/payment-providers`}>
            <Button className="w-full group">
              <span>Gerenciar Adquirentes</span>
              <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
