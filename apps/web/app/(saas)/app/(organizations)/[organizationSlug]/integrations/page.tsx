import { IntegrationsClient } from "./components/IntegrationsClient";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";

export default async function IntegrationsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return <IntegrationsClient organizationId={organization.id} organizationSlug={organizationSlug} />;
}
