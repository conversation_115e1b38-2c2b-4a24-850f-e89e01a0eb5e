"use client";

import { useState } from "react";
import { PaymentProvider } from "./types";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Switch } from "@ui/components/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { Badge } from "@ui/components/badge";
import { Separator } from "@ui/components/separator";
import {
  Settings,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle,
  Info,
  ExternalLink
} from "lucide-react";
import Image from "next/image";

interface GatewayConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  provider: PaymentProvider;
  onSuccess?: () => void;
}

export function GatewayConfigModal({
  open,
  onOpenChange,
  provider,
  onSuccess
}: GatewayConfigModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showCredentials, setShowCredentials] = useState(false);
  const [formData, setFormData] = useState({
    // Credenciais básicas
    apiKey: "",
    apiSecret: "",
    clientId: "",
    clientSecret: "",
    // Configurações específicas
    environment: "sandbox", // sandbox ou production
    webhookUrl: "",
    // Configurações de PIX
    pixKey: "",
    pixCertPath: "",
    // Configurações de cartão
    merchantId: "",
    // Configurações gerais
    isActive: false,
    isDefault: false,
    priority: 1,
    description: "",
  });

  const handleInputChange = (field: string, value: string | boolean | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Simular configuração do gateway
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Aqui você faria a chamada real para a API
      console.log("Configurando gateway:", provider.id, formData);

      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao configurar gateway:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRequiredFields = () => {
    switch (provider.id) {
      case "pluggou-pix":
        return ["apiKey", "apiSecret", "pixKey"];
      case "asaas":
        return ["apiKey", "environment"];
      case "mercado-pago":
        return ["clientId", "clientSecret", "environment"];
      case "owempay":
        return ["apiKey", "pixKey"];
      case "pagarme":
        return ["apiKey", "environment"];
      case "transfeera":
        return ["apiKey", "pixKey"];
      case "zendry":
        return ["apiKey", "pixKey"];
      default:
        return ["apiKey"];
    }
  };

  const requiredFields = getRequiredFields();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="relative h-12 w-12 overflow-hidden rounded-md bg-white p-2 flex items-center justify-center">
              {provider.logo ? (
                <Image
                  src={provider.logo}
                  alt={provider.name}
                  width={50}
                  height={50}
                  className="object-contain"
                />
              ) : (
                <Settings className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
            <div>
              <DialogTitle className="text-xl">{provider.name}</DialogTitle>
              <DialogDescription>
                Configure as credenciais e opções do gateway
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Informações do Gateway */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informações do Gateway</CardTitle>
              <CardDescription>
                {provider.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {Object.entries(provider.features).map(([feature, enabled]) => {
                  if (!enabled) return null;
                  return (
                    <Badge key={feature} variant="secondary" className="text-xs">
                      {feature.charAt(0).toUpperCase() + feature.slice(1)}
                    </Badge>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Credenciais Básicas */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Credenciais</CardTitle>
              <CardDescription>
                Informe as credenciais fornecidas pelo gateway
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="apiKey">
                    API Key {requiredFields.includes("apiKey") && <span className="text-red-500">*</span>}
                  </Label>
                  <div className="relative">
                    <Input
                      id="apiKey"
                      type={showCredentials ? "text" : "password"}
                      value={formData.apiKey}
                      onChange={(e) => handleInputChange("apiKey", e.target.value)}
                      placeholder="Sua chave de API"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowCredentials(!showCredentials)}
                    >
                      {showCredentials ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="apiSecret">
                    API Secret {requiredFields.includes("apiSecret") && <span className="text-red-500">*</span>}
                  </Label>
                  <Input
                    id="apiSecret"
                    type={showCredentials ? "text" : "password"}
                    value={formData.apiSecret}
                    onChange={(e) => handleInputChange("apiSecret", e.target.value)}
                    placeholder="Sua chave secreta"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientId">
                    Client ID {requiredFields.includes("clientId") && <span className="text-red-500">*</span>}
                  </Label>
                  <Input
                    id="clientId"
                    value={formData.clientId}
                    onChange={(e) => handleInputChange("clientId", e.target.value)}
                    placeholder="ID do cliente"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientSecret">
                    Client Secret {requiredFields.includes("clientSecret") && <span className="text-red-500">*</span>}
                  </Label>
                  <Input
                    id="clientSecret"
                    type={showCredentials ? "text" : "password"}
                    value={formData.clientSecret}
                    onChange={(e) => handleInputChange("clientSecret", e.target.value)}
                    placeholder="Segredo do cliente"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Configurações de PIX */}
          {(provider.features.pix || provider.category === "pix") && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Configurações PIX</CardTitle>
                <CardDescription>
                  Configurações específicas para pagamentos PIX
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="pixKey">
                    Chave PIX {requiredFields.includes("pixKey") && <span className="text-red-500">*</span>}
                  </Label>
                  <Input
                    id="pixKey"
                    value={formData.pixKey}
                    onChange={(e) => handleInputChange("pixKey", e.target.value)}
                    placeholder="Sua chave PIX (CPF, CNPJ, email ou telefone)"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pixCertPath">Caminho do Certificado</Label>
                  <Input
                    id="pixCertPath"
                    value={formData.pixCertPath}
                    onChange={(e) => handleInputChange("pixCertPath", e.target.value)}
                    placeholder="/path/to/certificate.p12"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Configurações Gerais */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Configurações Gerais</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="environment">Ambiente</Label>
                <select
                  id="environment"
                  value={formData.environment}
                  onChange={(e) => handleInputChange("environment", e.target.value)}
                  className="w-full p-2 border border-input rounded-md"
                >
                  <option value="sandbox">Sandbox (Teste)</option>
                  <option value="production">Produção</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="webhookUrl">URL do Webhook</Label>
                <Input
                  id="webhookUrl"
                  value={formData.webhookUrl}
                  onChange={(e) => handleInputChange("webhookUrl", e.target.value)}
                  placeholder="https://seu-site.com/webhook"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Prioridade</Label>
                <Input
                  id="priority"
                  type="number"
                  min="1"
                  max="10"
                  value={formData.priority}
                  onChange={(e) => handleInputChange("priority", parseInt(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Descrição</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Descrição opcional para este gateway"
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="isActive">Gateway Ativo</Label>
                    <p className="text-sm text-muted-foreground">
                      Este gateway estará disponível para processar pagamentos
                    </p>
                  </div>
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange("isActive", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="isDefault">Gateway Padrão</Label>
                    <p className="text-sm text-muted-foreground">
                      Este será o gateway padrão para novos pagamentos
                    </p>
                  </div>
                  <Switch
                    id="isDefault"
                    checked={formData.isDefault}
                    onCheckedChange={(checked) => handleInputChange("isDefault", checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações de Ajuda */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900">Precisa de ajuda?</h4>
                  <p className="text-sm text-blue-700">
                    Consulte a documentação do {provider.name} para obter suas credenciais de API.
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="text-blue-600 border-blue-300 hover:bg-blue-100"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Ver Documentação
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Configurando...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Configurar Gateway
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
