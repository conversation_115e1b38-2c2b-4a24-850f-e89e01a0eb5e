"use client";

import { useState } from "react";
import { PaymentProvider } from "./types";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardFooter } from "@ui/components/card";
import {
  CheckCircle2,
  Settings,
  Star,
  CreditCard,
  Shield,
  FileText,
  Webhook,
  BarChart3
} from "lucide-react";
import { FaPix } from "react-icons/fa6";
import Image from "next/image";
import { GatewayConfigModal } from "./GatewayConfigModal";

interface PaymentProviderCardProps {
  provider: PaymentProvider;
  onInstall: (providerId: string) => void;
}

export function PaymentProviderCard({ provider, onInstall }: PaymentProviderCardProps) {
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);

  const getFeatureIcon = (feature: string) => {
    switch (feature) {
      case "pix":
        return <FaPix className="h-3 w-3" />;
      case "cartao":
        return <CreditCard className="h-3 w-3" />;
      case "boleto":
        return <FileText className="h-3 w-3" />;
      case "antifraude":
        return <Shield className="h-3 w-3" />;
      case "webhook":
        return <Webhook className="h-3 w-3" />;
      case "relatorios":
        return <BarChart3 className="h-3 w-3" />;
      default:
        return <CheckCircle2 className="h-3 w-3" />;
    }
  };

  const getFeatureColor = (feature: string) => {
    switch (feature) {
      case "pix":
        return "bg-blue-100 text-blue-700";
      case "cartao":
        return "bg-green-100 text-green-700";
      case "boleto":
        return "bg-orange-100 text-orange-700";
      case "antifraude":
        return "bg-red-100 text-red-700";
      case "webhook":
        return "bg-purple-100 text-purple-700";
      case "relatorios":
        return "bg-indigo-100 text-indigo-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const getFeatureLabel = (feature: string) => {
    switch (feature) {
      case "pix":
        return "PIX";
      case "cartao":
        return "Cartão";
      case "boleto":
        return "Boleto";
      case "antifraude":
        return "Antifraude";
      case "webhook":
        return "Webhook";
      case "relatorios":
        return "Relatórios";
      default:
        return feature;
    }
  };

  const handleConfigure = () => {
    setModalOpen(true);
  };

  const handleModalSuccess = () => {
    setModalOpen(false);
    onInstall(provider.id);
  };

  return (
    <Card className={`overflow-hidden ${provider.highlight ? 'border-primary/50' : ''}`}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between gap-4 mb-4">
          <div className="flex items-center gap-3">
            <div className="relative h-12 w-12 overflow-hidden rounded-md bg-white p-2 flex items-center justify-center">
              {provider.logo ? (
                <Image
                  src={provider.logo}
                  alt={provider.name}
                  width={50}
                  height={50}
                  className="object-contain"
                />
              ) : (
                <Settings className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
            <div>
              <h3 className="font-medium text-lg leading-none">{provider.name}</h3>
              <p className="text-sm text-muted-foreground mt-1">
                {provider.id.toUpperCase()}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {provider.highlight && (
              <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            )}
            {provider.isNew && (
              <Badge variant="default" className="bg-primary text-primary-foreground text-xs">
                NOVO
              </Badge>
            )}
            {provider.isInstalled && (
              <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                Ativo
              </Badge>
            )}
          </div>
        </div>

        <p className="text-sm text-muted-foreground mb-4">
          {provider.description}
        </p>

        <div className="flex flex-wrap gap-2 mb-4">
          {Object.entries(provider.features).map(([feature, enabled]) => {
            if (!enabled) return null;
            return (
              <div
                key={feature}
                className={`flex items-center text-xs rounded-full px-2 py-1 ${getFeatureColor(feature)}`}
              >
                {getFeatureIcon(feature)}
                <span className="ml-1">{getFeatureLabel(feature)}</span>
              </div>
            );
          })}
        </div>
      </CardContent>

      <CardFooter className="px-6 py-4 bg-muted/20 border-t">
        <Button
          onClick={handleConfigure}
          variant={provider.isInstalled ? "outline" : "default"}
          className="flex-1"
          disabled={isConfiguring}
        >
          <Settings className="h-4 w-4 mr-2" />
          {isConfiguring ? "Configurando..." : "Configurar gateway"}
        </Button>
      </CardFooter>

      <GatewayConfigModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        provider={provider}
        onSuccess={handleModalSuccess}
      />
    </Card>
  );
}
