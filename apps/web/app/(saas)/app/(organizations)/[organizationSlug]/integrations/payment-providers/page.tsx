import { PaymentProvidersClient } from "./components/PaymentProvidersClient";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { notFound } from "next/navigation";

export default async function PaymentProvidersPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Adquirentes de Pagamento"
        subtitle="Conecte-se com os principais provedores de pagamento do Brasil e do mundo"
      />

      <PaymentProvidersClient organizationId={organization.id} />
    </div>
  );
}
