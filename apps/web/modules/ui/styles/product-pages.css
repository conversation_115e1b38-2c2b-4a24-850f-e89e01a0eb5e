/* Product Pages Styling Utilities */

/* Enhanced card shadows for better depth */
.product-card {
  @apply shadow-sm border-border/50 hover:shadow-lg transition-all duration-200;
}

/* Consistent form field styling */
.product-form-field {
  @apply h-11 transition-colors;
}

/* Enhanced error states */
.product-form-error {
  @apply text-sm text-destructive flex items-center gap-1;
}

.product-form-error::before {
  content: '';
  @apply w-1 h-1 bg-destructive rounded-full;
}

/* Stats card styling */
.product-stats-card {
  @apply shadow-sm border-border/50 hover:shadow-md transition-shadow duration-200;
}

/* Loading skeleton animations */
.product-skeleton {
  @apply bg-muted rounded animate-pulse;
}

/* Enhanced button styling for product pages */
.product-primary-button {
  @apply h-11 px-8 shadow-sm hover:shadow-md transition-all duration-200;
}

.product-secondary-button {
  @apply h-11 px-6 border-border/50 hover:bg-muted/50 transition-all duration-200;
}

/* Information banner styling */
.product-info-banner {
  @apply bg-blue-50/80 dark:bg-blue-950/30 border border-blue-200/60 dark:border-blue-800/50 rounded-xl p-5 shadow-sm;
}

.product-info-banner-icon {
  @apply w-6 h-6 rounded-full bg-blue-500 dark:bg-blue-400 flex items-center justify-center flex-shrink-0 mt-0.5 shadow-sm;
}

.product-info-banner-title {
  @apply font-semibold text-blue-900 dark:text-blue-100 mb-2 text-base;
}

.product-info-banner-text {
  @apply text-sm text-blue-800 dark:text-blue-200 leading-relaxed;
}

/* Empty state styling */
.product-empty-state {
  @apply shadow-sm border-border/50;
}

.product-empty-state-icon {
  @apply w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mb-6;
}

/* Error state styling */
.product-error-state {
  @apply shadow-sm border-destructive/50 bg-destructive/5;
}

.product-error-state-icon {
  @apply w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mb-6;
}

/* Responsive grid improvements */
@media (min-width: 768px) {
  .product-grid {
    @apply grid-cols-2;
  }
}

@media (min-width: 1024px) {
  .product-grid {
    @apply grid-cols-3;
  }
}

@media (min-width: 1280px) {
  .product-grid {
    @apply grid-cols-4;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .product-card {
    @apply bg-card/50 backdrop-blur-sm;
  }
  
  .product-stats-card {
    @apply bg-card/50 backdrop-blur-sm;
  }
}

/* Focus states for accessibility */
.product-form-field:focus {
  @apply ring-2 ring-primary/20 border-primary;
}

.product-primary-button:focus {
  @apply ring-2 ring-primary/20;
}

.product-secondary-button:focus {
  @apply ring-2 ring-muted-foreground/20;
}

/* Animation utilities */
.product-fade-in {
  animation: productFadeIn 0.3s ease-out;
}

@keyframes productFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-slide-up {
  animation: productSlideUp 0.4s ease-out;
}

@keyframes productSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading spinner for buttons */
.product-loading-spinner {
  @apply w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin;
}
