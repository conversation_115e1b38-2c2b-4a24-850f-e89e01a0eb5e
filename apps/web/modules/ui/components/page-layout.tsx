"use client";

import * as React from "react";
import { cn } from "@ui/lib";
import { But<PERSON> } from "@ui/components/button";
import { 
  Breadcrumb, 
  BreadcrumbList, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from "@ui/components/breadcrumb";
import { FilterIcon, SearchIcon } from "lucide-react";
import { Input } from "@ui/components/input";
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetHeader, 
  SheetTitle, 
  SheetTrigger 
} from "@ui/components/sheet";

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface PageLayoutProps {
  children: React.ReactNode;
  className?: string;
  
  // Header section
  title: string;
  description?: string;
  breadcrumbs?: BreadcrumbItem[];
  
  // Actions section
  primaryAction?: React.ReactNode;
  secondaryActions?: React.ReactNode[];
  
  // Search and filters
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  filterContent?: React.ReactNode;
  showSearch?: boolean;
  showFilters?: boolean;
  
  // Layout options
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full";
  spacing?: "sm" | "md" | "lg";
}

export function PageLayout({
  children,
  className,
  title,
  description,
  breadcrumbs = [],
  primaryAction,
  secondaryActions = [],
  searchPlaceholder = "Buscar...",
  searchValue,
  onSearchChange,
  filterContent,
  showSearch = false,
  showFilters = false,
  maxWidth = "full",
  spacing = "md",
}: PageLayoutProps) {
  const maxWidthClasses = {
    sm: "max-w-sm",
    md: "max-w-md", 
    lg: "max-w-4xl",
    xl: "max-w-6xl",
    "2xl": "max-w-7xl",
    full: "max-w-none",
  };

  const spacingClasses = {
    sm: "space-y-4",
    md: "space-y-6",
    lg: "space-y-8",
  };

  return (
    <div className={cn("w-full", maxWidthClasses[maxWidth], className)}>
      <div className={cn(spacingClasses[spacing])}>
        {/* Breadcrumbs */}
        {breadcrumbs.length > 0 && (
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((item, index) => (
                <React.Fragment key={index}>
                  <BreadcrumbItem>
                    {item.href ? (
                      <BreadcrumbLink href={item.href}>
                        {item.label}
                      </BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage>{item.label}</BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                  {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                </React.Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        )}

        {/* Header Section */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            {description && (
              <p className="text-muted-foreground">{description}</p>
            )}
          </div>
          
          {/* Primary Action */}
          {primaryAction && (
            <div className="flex shrink-0">
              {primaryAction}
            </div>
          )}
        </div>

        {/* Search and Filters Bar */}
        {(showSearch || showFilters || secondaryActions.length > 0) && (
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex flex-1 items-center gap-2">
              {/* Search */}
              {showSearch && (
                <div className="relative flex-1 max-w-sm">
                  <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder={searchPlaceholder}
                    value={searchValue}
                    onChange={(e) => onSearchChange?.(e.target.value)}
                    className="pl-9"
                  />
                </div>
              )}
              
              {/* Filters */}
              {showFilters && filterContent && (
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm">
                      <FilterIcon className="h-4 w-4 mr-2" />
                      Filtros
                    </Button>
                  </SheetTrigger>
                  <SheetContent>
                    <SheetHeader>
                      <SheetTitle>Filtros</SheetTitle>
                      <SheetDescription>
                        Use os filtros abaixo para refinar sua busca.
                      </SheetDescription>
                    </SheetHeader>
                    <div className="mt-6">
                      {filterContent}
                    </div>
                  </SheetContent>
                </Sheet>
              )}
            </div>
            
            {/* Secondary Actions */}
            {secondaryActions.length > 0 && (
              <div className="flex items-center gap-2">
                {secondaryActions.map((action, index) => (
                  <React.Fragment key={index}>{action}</React.Fragment>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Content Area */}
        <div className="flex-1">
          {children}
        </div>
      </div>
    </div>
  );
}
