"use client";

import { But<PERSON> } from "@ui/components/button";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Separator } from "@ui/components/separator";
import { XIcon } from "lucide-react";

export interface FilterOption {
  value: string;
  label: string;
}

export interface FilterConfig {
  key: string;
  label: string;
  placeholder: string;
  options: FilterOption[];
}

interface FiltersProps {
  filters: FilterConfig[];
  values: Record<string, string | undefined>;
  onFilterChange: (key: string, value: string | undefined) => void;
  onClearFilters: () => void;
  className?: string;
}

export function Filters({
  filters,
  values,
  onFilterChange,
  onClearFilters,
  className = ""
}: FiltersProps) {
  const hasActiveFilters = Object.values(values).some(value => value !== undefined);

  return (
    <div className={`space-y-6 ${className}`}>
      {filters.map((filter) => (
        <div key={filter.key} className="space-y-2">
          <Label htmlFor={`${filter.key}-filter`}>{filter.label}</Label>
          <Select
            value={values[filter.key] || undefined}
            onValueChange={(value) => onFilterChange(filter.key, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={filter.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {filter.options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      ))}

      <Separator />

      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onClearFilters}
          disabled={!hasActiveFilters}
          className="flex items-center gap-2"
        >
          <XIcon className="h-4 w-4" />
          Limpar Filtros
        </Button>
      </div>
    </div>
  );
}
