"use client"

import * as React from "react"
import { Area, AreaChart, Bar, BarChart, CartesianGrid, Pie, Pie<PERSON>hart, XAxis, YAxis } from "recharts"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "./chart"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./card"

// Revenue Chart Component
interface RevenueChartProps {
  data: Array<{
    month: string
    revenue: number
    transactions: number
  }>
  className?: string
}

export function RevenueChart({ data, className }: RevenueChartProps) {
  const chartConfig = {
    revenue: {
      label: "<PERSON>ce<PERSON>",
      color: "#10b981", // Emerald-500
    },
    transactions: {
      label: "Transações",
      color: "#3b82f6", // Blue-500
    },
  } satisfies ChartConfig

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle><PERSON><PERSON><PERSON></CardTitle>
        <CardDescription>
          Evolução da receita e transações ao longo dos meses
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <AreaChart data={data}>
            <defs>
              <linearGradient id="fillRevenue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => `Mês: ${value}`}
                  formatter={(value, name) => [
                    name === "revenue" ? `R$ ${value.toLocaleString()}` : value,
                    name === "revenue" ? "Receita" : "Transações"
                  ]}
                />
              }
            />
            <Area
              type="monotone"
              dataKey="revenue"
              stroke="#10b981"
              fill="url(#fillRevenue)"
              strokeWidth={2}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

// Transactions Chart Component
interface TransactionsChartProps {
  data: Array<{
    month: string
    transactions: number
  }>
  className?: string
}

export function TransactionsChart({ data, className }: TransactionsChartProps) {
  const chartConfig = {
    transactions: {
      label: "Transações",
      color: "#8b5cf6", // Violet-500
    },
  } satisfies ChartConfig

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Transações Mensais</CardTitle>
        <CardDescription>
          Volume de transações processadas por mês
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => `Mês: ${value}`}
                  formatter={(value) => [value, "Transações"]}
                />
              }
            />
            <Bar
              dataKey="transactions"
              fill="#8b5cf6"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

// Payment Methods Pie Chart Component
interface PaymentMethodsChartProps {
  data: Array<{
    method: string
    percentage: number
    count: number
    value: number
  }>
  className?: string
}

export function PaymentMethodsChart({ data, className }: PaymentMethodsChartProps) {
  const chartConfig = {
    PIX: {
      label: "PIX",
      color: "#10b981", // Emerald-500
    },
    "Cartão de Crédito": {
      label: "Cartão de Crédito",
      color: "#3b82f6", // Blue-500
    },
    Boleto: {
      label: "Boleto",
      color: "#f59e0b", // Amber-500
    },
  } satisfies ChartConfig

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Métodos de Pagamento</CardTitle>
        <CardDescription>
          Distribuição por método de pagamento
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <PieChart>
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name, props) => [
                    `${props.payload.percentage}%`,
                    name
                  ]}
                />
              }
            />
            <Pie
              data={data.map(item => ({
                ...item,
                fill: chartConfig[item.method as keyof typeof chartConfig]?.color || "#8884d8"
              }))}
              dataKey="value"
              nameKey="method"
              cx="50%"
              cy="50%"
              outerRadius={80}
              label={({ percentage }) => `${percentage}%`}
            />
          </PieChart>
        </ChartContainer>
        <div className="mt-4 space-y-2">
          {data.map((item) => (
            <div key={item.method} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{
                    backgroundColor: chartConfig[item.method as keyof typeof chartConfig]?.color || "#8884d8"
                  }}
                />
                <span className="text-sm font-medium">{item.method}</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-semibold">{item.percentage}%</div>
                <div className="text-xs text-muted-foreground">
                  {item.count.toLocaleString()} transações
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

// Revenue by Payment Method Chart
interface RevenueByPaymentMethodProps {
  data: Array<{
    method: string
    revenue: number
    percentage: number
  }>
  className?: string
}

export function RevenueByPaymentMethodChart({ data, className }: RevenueByPaymentMethodProps) {
  const chartConfig = {
    PIX: {
      label: "PIX",
      color: "#10b981", // Emerald-500
    },
    "Cartão de Crédito": {
      label: "Cartão de Crédito",
      color: "#3b82f6", // Blue-500
    },
    Boleto: {
      label: "Boleto",
      color: "#f59e0b", // Amber-500
    },
  } satisfies ChartConfig

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Receita por Método de Pagamento</CardTitle>
        <CardDescription>
          Distribuição da receita por forma de pagamento
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <BarChart data={data} layout="horizontal">
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              type="number"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
            />
            <YAxis
              dataKey="method"
              type="category"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              width={120}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name) => [
                    `R$ ${value.toLocaleString()}`,
                    name
                  ]}
                />
              }
            />
            <Bar
              dataKey="revenue"
              fill="#10b981"
              radius={[0, 4, 4, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
