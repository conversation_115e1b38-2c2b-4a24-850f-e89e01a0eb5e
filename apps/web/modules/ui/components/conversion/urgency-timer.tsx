'use client';

import { useState, useEffect } from 'react';
import { Clock, AlertTriangle } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { cn } from '@ui/lib';

interface UrgencyTimerProps {
  endTime?: Date;
  message?: string;
  showIcon?: boolean;
  variant?: 'default' | 'warning' | 'danger';
  className?: string;
}

export function UrgencyTimer({
  endTime,
  message = 'Oferta termina em:',
  showIcon = true,
  variant = 'warning',
  className = ''
}: UrgencyTimerProps) {
  const [timeLeft, setTimeLeft] = useState<{
    hours: number;
    minutes: number;
    seconds: number;
  }>({ hours: 0, minutes: 0, seconds: 0 });

  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    if (!endTime) return;

    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const end = endTime.getTime();
      const difference = end - now;

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ hours, minutes, seconds });
        setIsExpired(false);
      } else {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
        setIsExpired(true);
      }
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [endTime]);

  if (isExpired) {
    return (
      <Card className={cn(
        'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20',
        className
      )}>
        <CardContent className="p-4">
          <div className="flex items-center justify-center gap-2 text-red-600 dark:text-red-400">
            <AlertTriangle className="h-4 w-4" />
            <span className="font-medium">Oferta expirada</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20 text-red-800 dark:text-red-200';
      case 'warning':
        return 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20 text-orange-800 dark:text-orange-200';
      default:
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20 text-blue-800 dark:text-blue-200';
    }
  };

  return (
    <Card className={cn(
      'border-2 animate-pulse',
      getVariantStyles(),
      className
    )}>
      <CardContent className="p-4">
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-3">
            {showIcon && <Clock className="h-4 w-4" />}
            <span className="font-medium text-sm">{message}</span>
          </div>

          <div className="flex items-center justify-center gap-2">
            <div className="flex flex-col items-center">
              <Badge variant="secondary" className="text-lg font-bold px-3 py-1">
                {timeLeft.hours.toString().padStart(2, '0')}
              </Badge>
              <span className="text-xs mt-1">horas</span>
            </div>

            <span className="text-2xl font-bold">:</span>

            <div className="flex flex-col items-center">
              <Badge variant="secondary" className="text-lg font-bold px-3 py-1">
                {timeLeft.minutes.toString().padStart(2, '0')}
              </Badge>
              <span className="text-xs mt-1">min</span>
            </div>

            <span className="text-2xl font-bold">:</span>

            <div className="flex flex-col items-center">
              <Badge variant="secondary" className="text-lg font-bold px-3 py-1">
                {timeLeft.seconds.toString().padStart(2, '0')}
              </Badge>
              <span className="text-xs mt-1">seg</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Hook para criar timer de urgência baseado em duração
export function useUrgencyTimer(durationHours: number = 24) {
  const [endTime, setEndTime] = useState<Date | null>(null);

  useEffect(() => {
    const now = new Date();
    const end = new Date(now.getTime() + durationHours * 60 * 60 * 1000);
    setEndTime(end);
  }, [durationHours]);

  return endTime;
}
