'use client';

import { useState, useEffect } from 'react';
import { Star, Quote, ChevronLeft, ChevronRight, Shield, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Badge } from '@ui/components/badge';
import { cn } from '@ui/lib';

interface Testimonial {
  id: string;
  customerName: string;
  customerPhoto?: string;
  customerRole?: string;
  customerLocation?: string;
  content: string;
  rating: number;
  source?: string;
  isVerified?: boolean;
}

interface TestimonialsProps {
  testimonials?: Testimonial[];
  maxTestimonials?: number;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showControls?: boolean;
  showSource?: boolean;
  showVerification?: boolean;
  className?: string;
}

const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    customerName: '<PERSON>',
    customerRole: 'Empresário',
    customerLocation: 'São Paulo, SP',
    content: 'A integração foi surpreendentemente simples. Em menos de 2 horas já estávamos processando pagamentos PIX. A documentação é excelente e o suporte técnico respondeu todas nossas dúvidas rapidamente.',
    rating: 5,
    source: 'WhatsApp',
    isVerified: true,
  },
  {
    id: '2',
    customerName: 'Ana Rodrigues',
    customerRole: 'Head de Produto',
    customerLocation: 'Rio de Janeiro, RJ',
    content: 'Desde que implementamos a solução, nossa taxa de abandono de carrinho diminuiu drasticamente. Os clientes adoram a facilidade do PIX e a segurança dos pagamentos com cartão.',
    rating: 5,
    source: 'Facebook',
    isVerified: true,
  },
  {
    id: '3',
    customerName: 'Roberto Santos',
    customerRole: 'Founder',
    customerLocation: 'Belo Horizonte, MG',
    content: 'Como startup, precisávamos de uma solução confiável e escalável. O gateway nos permitiu focar no nosso produto principal enquanto eles cuidam de toda a complexidade dos pagamentos.',
    rating: 5,
    source: 'Instagram',
    isVerified: true,
  },
  {
    id: '4',
    customerName: 'Mariana Costa',
    customerRole: 'CFO',
    customerLocation: 'Porto Alegre, RS',
    content: 'A transparência nos relatórios e a facilidade de integração foram os pontos que mais me chamaram atenção. Conseguimos reduzir custos operacionais significativamente.',
    rating: 5,
    source: 'Email',
    isVerified: true,
  },
];

export function Testimonials({
  testimonials = defaultTestimonials,
  maxTestimonials = 3,
  autoPlay = true,
  autoPlayInterval = 5000,
  showControls = true,
  showSource = true,
  showVerification = true,
  className = ''
}: TestimonialsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);

  const displayTestimonials = testimonials.slice(0, maxTestimonials);

  useEffect(() => {
    if (!isPlaying || displayTestimonials.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isPlaying, displayTestimonials.length, autoPlayInterval]);

  const goToPrevious = () => {
    setCurrentIndex((prev) =>
      prev === 0 ? displayTestimonials.length - 1 : prev - 1
    );
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  if (displayTestimonials.length === 0) {
    return null;
  }

  const currentTestimonial = displayTestimonials[currentIndex];

  return (
    <div className={cn('w-full', className)}>
      <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
        <CardContent className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Quote className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                O que nossos clientes dizem
              </h3>
            </div>

            {showVerification && (
              <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                <CheckCircle className="h-3 w-3 mr-1" />
                Verificado
              </Badge>
            )}
          </div>

          {/* Testimonial Content */}
          <div className="relative">
            <div className="text-center">
              {/* Rating */}
              <div className="flex justify-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={cn(
                      'h-4 w-4',
                      i < currentTestimonial.rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    )}
                  />
                ))}
              </div>

              {/* Quote */}
              <blockquote className="text-gray-700 dark:text-gray-300 text-base leading-relaxed mb-4">
                "{currentTestimonial.content}"
              </blockquote>

              {/* Customer Info */}
              <div className="flex items-center justify-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage
                    src={currentTestimonial.customerPhoto}
                    alt={currentTestimonial.customerName}
                  />
                  <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                    {currentTestimonial.customerName.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>

                <div className="text-left">
                  <div className="font-semibold text-gray-900 dark:text-gray-100">
                    {currentTestimonial.customerName}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {currentTestimonial.customerRole}
                    {currentTestimonial.customerLocation && (
                      <span> • {currentTestimonial.customerLocation}</span>
                    )}
                  </div>
                  {showSource && currentTestimonial.source && (
                    <div className="text-xs text-gray-500 dark:text-gray-500">
                      via {currentTestimonial.source}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Controls */}
            {showControls && displayTestimonials.length > 1 && (
              <div className="flex items-center justify-between mt-6">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevious}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Dots Indicator */}
                <div className="flex gap-2">
                  {displayTestimonials.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => goToSlide(index)}
                      className={cn(
                        'h-2 w-2 rounded-full transition-all',
                        index === currentIndex
                          ? 'bg-blue-600 w-6'
                          : 'bg-gray-300 hover:bg-gray-400'
                      )}
                    />
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNext}
                  className="h-8 w-8 p-0"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Play/Pause Button */}
            {displayTestimonials.length > 1 && (
              <div className="flex justify-center mt-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={togglePlayPause}
                  className="text-gray-500 hover:text-gray-700"
                >
                  {isPlaying ? 'Pausar' : 'Reproduzir'}
                </Button>
              </div>
            )}
          </div>

          {/* Trust Indicators */}
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-center gap-6 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Shield className="h-4 w-4" />
                <span>100% Seguro</span>
              </div>
              <div className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4" />
                <span>Garantia de 30 dias</span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span>4.9/5 avaliação</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
