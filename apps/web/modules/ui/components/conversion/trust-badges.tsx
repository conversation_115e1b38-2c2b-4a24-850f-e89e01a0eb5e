'use client';

import { Shield, Lock, CheckCircle, Award, Truck, CreditCard } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { cn } from '@ui/lib';

interface TrustBadge {
  id: string;
  icon: React.ReactNode;
  title: string;
  description?: string;
  color?: 'blue' | 'green' | 'purple' | 'orange';
}

interface TrustBadgesProps {
  badges?: TrustBadge[];
  layout?: 'horizontal' | 'vertical' | 'grid';
  showDescriptions?: boolean;
  className?: string;
}

const defaultBadges: TrustBadge[] = [
  {
    id: 'security',
    icon: <Shield className="h-5 w-5" />,
    title: '100% Seguro',
    description: 'Pagamentos protegidos com criptografia SSL',
    color: 'green',
  },
  {
    id: 'guarantee',
    icon: <CheckCircle className="h-5 w-5" />,
    title: 'Garantia de 30 dias',
    description: 'Devolução do dinheiro se não ficar satisfeito',
    color: 'blue',
  },
  {
    id: 'delivery',
    icon: <Truck className="h-5 w-5" />,
    title: 'Entrega Imediata',
    description: 'Acesso instantâneo após confirmação do pagamento',
    color: 'purple',
  },
  {
    id: 'payment',
    icon: <CreditCard className="h-5 w-5" />,
    title: 'Pagamento Seguro',
    description: 'Cartão de crédito, PIX e boleto bancário',
    color: 'orange',
  },
  {
    id: 'award',
    icon: <Award className="h-5 w-5" />,
    title: 'Certificado de Qualidade',
    description: 'Produto aprovado por especialistas',
    color: 'green',
  },
  {
    id: 'lock',
    icon: <Lock className="h-5 w-5" />,
    title: 'Dados Protegidos',
    description: 'Seus dados pessoais são mantidos em segurança',
    color: 'blue',
  },
];

export function TrustBadges({
  badges = defaultBadges,
  layout = 'horizontal',
  showDescriptions = false,
  className = ''
}: TrustBadgesProps) {
  const getColorStyles = (color: string) => {
    switch (color) {
      case 'green':
        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800';
      case 'blue':
        return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800';
      case 'purple':
        return 'text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-950/20 border-purple-200 dark:border-purple-800';
      case 'orange':
        return 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-950/20 border-gray-200 dark:border-gray-800';
    }
  };

  const getLayoutStyles = () => {
    switch (layout) {
      case 'vertical':
        return 'flex-col space-y-2';
      case 'grid':
        return 'grid grid-cols-2 gap-2';
      default:
        return 'flex-row flex-wrap gap-2';
    }
  };

  return (
    <div className={cn('w-full', className)}>
      <Card className="border-0 shadow-sm">
        <CardContent className="p-4">
          <div className={cn('flex items-center justify-center', getLayoutStyles())}>
            {badges.map((badge) => (
              <div
                key={badge.id}
                className={cn(
                  'flex items-center gap-2 px-3 py-2 rounded-lg border transition-all hover:scale-105',
                  getColorStyles(badge.color || 'blue'),
                  layout === 'vertical' ? 'w-full justify-start' : 'flex-shrink-0'
                )}
              >
                {badge.icon}
                <div className="flex flex-col">
                  <span className="text-sm font-medium">{badge.title}</span>
                  {showDescriptions && badge.description && (
                    <span className="text-xs opacity-80">{badge.description}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Componente compacto para usar no checkout
export function TrustBadgesCompact({ className = '' }: { className?: string }) {
  const compactBadges = defaultBadges.slice(0, 4); // Apenas os 4 primeiros

  return (
    <div className={cn('flex flex-wrap gap-2 justify-center', className)}>
      {compactBadges.map((badge) => (
        <Badge
          key={badge.id}
          variant="secondary"
          className="flex items-center gap-1 px-2 py-1 text-xs"
        >
          {badge.icon}
          {badge.title}
        </Badge>
      ))}
    </div>
  );
}

// Componente de selos de segurança
export function SecuritySeals({ className = '' }: { className?: string }) {
  return (
    <div className={cn('flex items-center justify-center gap-4', className)}>
      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
        <Shield className="h-4 w-4 text-green-600" />
        <span>SSL Seguro</span>
      </div>
      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
        <Lock className="h-4 w-4 text-blue-600" />
        <span>Dados Protegidos</span>
      </div>
      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <span>Garantia</span>
      </div>
    </div>
  );
}
