'use client';

import { useState, useEffect } from 'react';
import { Users, AlertCircle, TrendingUp } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { cn } from '@ui/lib';

interface ScarcityProps {
  totalStock?: number;
  soldCount?: number;
  message?: string;
  showProgress?: boolean;
  showTrending?: boolean;
  variant?: 'default' | 'warning' | 'danger';
  className?: string;
}

export function Scarcity({
  totalStock = 100,
  soldCount = 0,
  message = 'Apenas {remaining} unidades restantes!',
  showProgress = true,
  showTrending = true,
  variant = 'warning',
  className = ''
}: ScarcityProps) {
  const [remaining, setRemaining] = useState(totalStock - soldCount);
  const [isLowStock, setIsLowStock] = useState(false);

  useEffect(() => {
    const remainingStock = totalStock - soldCount;
    setRemaining(remainingStock);
    setIsLowStock(remainingStock <= totalStock * 0.2); // 20% or less
  }, [totalStock, soldCount]);

  const percentage = (soldCount / totalStock) * 100;
  const progressPercentage = Math.min(percentage, 100);

  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20 text-red-800 dark:text-red-200';
      case 'warning':
        return 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20 text-orange-800 dark:text-orange-200';
      default:
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20 text-blue-800 dark:text-blue-200';
    }
  };

  const getProgressColor = () => {
    if (isLowStock) return 'bg-red-500';
    if (percentage > 70) return 'bg-orange-500';
    return 'bg-blue-500';
  };

  if (remaining <= 0) {
    return (
      <Card className={cn(
        'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20',
        className
      )}>
        <CardContent className="p-4">
          <div className="flex items-center justify-center gap-2 text-red-600 dark:text-red-400">
            <AlertCircle className="h-4 w-4" />
            <span className="font-medium">Esgotado!</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      'border-2',
      getVariantStyles(),
      className
    )}>
      <CardContent className="p-4">
        <div className="text-center">
          {/* Header */}
          <div className="flex items-center justify-center gap-2 mb-3">
            <Users className="h-4 w-4" />
            <span className="font-medium text-sm">
              {message.replace('{remaining}', remaining.toString())}
            </span>
            {showTrending && (
              <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                <TrendingUp className="h-3 w-3 mr-1" />
                Em alta
              </Badge>
            )}
          </div>

          {/* Progress Bar */}
          {showProgress && (
            <div className="mb-3">
              <div className="flex justify-between text-xs mb-1">
                <span>Vendidas: {soldCount}</span>
                <span>Restantes: {remaining}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={cn(
                    'h-2 rounded-full transition-all duration-300',
                    getProgressColor()
                  )}
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>
          )}

          {/* Stock Status */}
          <div className="text-xs">
            {isLowStock ? (
              <span className="text-red-600 dark:text-red-400 font-medium">
                ⚠️ Últimas unidades!
              </span>
            ) : percentage > 50 ? (
              <span className="text-orange-600 dark:text-orange-400 font-medium">
                🔥 Vendendo rápido!
              </span>
            ) : (
              <span className="text-blue-600 dark:text-blue-400 font-medium">
                ✅ Disponível
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Hook para simular vendas em tempo real
export function useScarcitySimulation(initialStock: number = 100) {
  const [soldCount, setSoldCount] = useState(0);
  const [isSimulating, setIsSimulating] = useState(false);

  useEffect(() => {
    if (!isSimulating) return;

    const interval = setInterval(() => {
      setSoldCount(prev => {
        const newCount = prev + Math.floor(Math.random() * 3) + 1;
        return Math.min(newCount, initialStock);
      });
    }, Math.random() * 10000 + 5000); // 5-15 seconds

    return () => clearInterval(interval);
  }, [isSimulating, initialStock]);

  const startSimulation = () => setIsSimulating(true);
  const stopSimulation = () => setIsSimulating(false);
  const resetSimulation = () => {
    setSoldCount(0);
    setIsSimulating(false);
  };

  return {
    soldCount,
    isSimulating,
    startSimulation,
    stopSimulation,
    resetSimulation,
  };
}
