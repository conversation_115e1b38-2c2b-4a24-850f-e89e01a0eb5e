"use client";

import { <PERSON>, CardContent, CardHeader } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";

interface ProductSkeletonProps {
  viewMode: "grid" | "list";
  count?: number;
}

export function ProductSkeleton({ viewMode, count = 6 }: ProductSkeletonProps) {
  const skeletons = Array.from({ length: count }, (_, i) => i);

  if (viewMode === "grid") {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {skeletons.map((i) => (
          <Card key={i} className="group relative overflow-hidden border-border/50 shadow-sm">
            {/* Image skeleton */}
            <div className="relative h-48 overflow-hidden bg-muted">
              <Skeleton className="w-full h-full" />
            </div>

            {/* Status badges skeleton */}
            <div className="absolute top-3 left-3">
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
            <div className="absolute top-3 right-3">
              <Skeleton className="h-6 w-20 rounded-full" />
            </div>

            {/* Content skeleton */}
            <CardHeader className="pb-3">
              <div className="space-y-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>

              {/* Price skeleton */}
              <div className="flex items-center justify-between pt-2">
                <Skeleton className="h-8 w-20 rounded-md" />
                <Skeleton className="h-4 w-16" />
              </div>
            </CardHeader>

            {/* Metrics skeleton */}
            <CardContent className="pt-0 pb-4">
              <div className="grid grid-cols-3 gap-3">
                {Array.from({ length: 3 }, (_, j) => (
                  <div key={j} className="text-center p-2 rounded-lg bg-muted/30">
                    <Skeleton className="h-4 w-4 mx-auto mb-1" />
                    <Skeleton className="h-4 w-8 mx-auto mb-1" />
                    <Skeleton className="h-3 w-12 mx-auto" />
                  </div>
                ))}
              </div>

              {/* Footer skeleton */}
              <div className="flex items-center justify-between mt-3 pt-3 border-t border-border/50">
                <Skeleton className="h-3 w-24" />
                <Skeleton className="h-7 w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {skeletons.map((i) => (
        <Card key={i} className="shadow-sm border-border/50">
          <CardHeader className="pb-4">
            <div className="flex items-center space-x-4">
              {/* Thumbnail skeleton */}
              <Skeleton className="w-16 h-16 rounded-lg" />

              {/* Product info skeleton */}
              <div className="space-y-2 flex-1">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>
                <Skeleton className="h-4 w-full" />
                <div className="flex items-center gap-4 mt-2">
                  <Skeleton className="h-3 w-16" />
                  <Skeleton className="h-3 w-16" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>

              {/* Right side skeleton */}
              <div className="space-y-2">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          </CardHeader>
        </Card>
      ))}
    </div>
  );
}
