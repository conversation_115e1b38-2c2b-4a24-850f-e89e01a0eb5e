"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from "@ui/components/sheet";
import { Calendar } from "@ui/components/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/popover";
import { Checkbox } from "@ui/components/checkbox";
import { Label } from "@ui/components/label";
import {
  FilterIcon,
  SearchIcon,
  CalendarIcon,
  XIcon,
  DownloadIcon,
  MapPinIcon,
  DollarSignIcon,
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@ui/lib";

export interface CustomerFilterState {
  searchTerm: string;
  status: string[];
  totalSpentRange: { min: number | null; max: number | null };
  location: string;
  joinDateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
}

interface CustomerFiltersProps {
  organizationId: string;
  onFiltersChange: (filters: CustomerFilterState) => void;
  onExport?: () => void;
}

const statusOptions = [
  { id: "active", name: "Ativo", color: "bg-green-100 text-green-800" },
  { id: "inactive", name: "Inativo", color: "bg-gray-100 text-gray-800" },
  { id: "pending", name: "Pendente", color: "bg-yellow-100 text-yellow-800" },
  { id: "vip", name: "VIP", color: "bg-purple-100 text-purple-800" },
];

export function CustomerFilters({ organizationId, onFiltersChange, onExport }: CustomerFiltersProps) {
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [filters, setFilters] = useState<CustomerFilterState>({
    searchTerm: "",
    status: [],
    totalSpentRange: { min: null, max: null },
    location: "",
    joinDateRange: { from: undefined, to: undefined },
  });

  const updateFilters = (newFilters: Partial<CustomerFilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  };

  const clearFilters = () => {
    const clearedFilters: CustomerFilterState = {
      searchTerm: "",
      status: [],
      totalSpentRange: { min: null, max: null },
      location: "",
      joinDateRange: { from: undefined, to: undefined },
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.status.length > 0) count++;
    if (filters.totalSpentRange.min !== null || filters.totalSpentRange.max !== null) count++;
    if (filters.location) count++;
    if (filters.joinDateRange.from || filters.joinDateRange.to) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="space-y-4">
      {/* Search and Quick Actions */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2 flex-1">
          {/* Search Input */}
          <div className="relative flex-1 max-w-md">
            <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Buscar clientes por nome, email ou telefone..."
              value={filters.searchTerm}
              onChange={(e) => updateFilters({ searchTerm: e.target.value })}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <Select
            value={filters.status.length === 1 ? filters.status[0] : "all"}
            onValueChange={(value) =>
              updateFilters({ status: value === "all" ? [] : [value] })
            }
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Todos os status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os status</SelectItem>
              {statusOptions.map((status) => (
                <SelectItem key={status.id} value={status.id}>
                  {status.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Action Buttons - Right aligned */}
        <div className="flex items-center gap-2">
          {/* Advanced Filters Button */}
          <Sheet open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="relative h-10">
                <FilterIcon className="h-4 w-4 mr-2" />
                Filtros
                {activeFiltersCount > 0 && (
                  <Badge status="info" className="ml-2 h-5 w-5 p-0 text-xs">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent className="w-[500px] sm:w-[600px]">
              <SheetHeader>
                <SheetTitle className="flex items-center gap-2">
                  <FilterIcon className="h-5 w-5" />
                  Filtros Avançados
                </SheetTitle>
              </SheetHeader>

              <div className="space-y-6 mt-6 max-h-[70vh] overflow-y-auto">
                {/* Location Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Localização</Label>
                  <Input
                    placeholder="Cidade ou estado"
                    value={filters.location}
                    onChange={(e) => updateFilters({ location: e.target.value })}
                  />
                </div>

                {/* Date Range Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Data de Cadastro</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !filters.joinDateRange.from && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {filters.joinDateRange.from ? (
                            format(filters.joinDateRange.from, "dd/MM/yyyy", { locale: ptBR })
                          ) : (
                            "Data inicial"
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={filters.joinDateRange.from || undefined}
                          onSelect={(date) => updateFilters({ joinDateRange: { ...filters.joinDateRange, from: date } })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !filters.joinDateRange.to && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {filters.joinDateRange.to ? (
                            format(filters.joinDateRange.to, "dd/MM/yyyy", { locale: ptBR })
                          ) : (
                            "Data final"
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={filters.joinDateRange.to || undefined}
                          onSelect={(date) => updateFilters({ joinDateRange: { ...filters.joinDateRange, to: date } })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Amount Range Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Valor Total Gasto</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      placeholder="Valor mínimo"
                      value={filters.totalSpentRange.min || ""}
                      onChange={(e) => updateFilters({
                        totalSpentRange: {
                          ...filters.totalSpentRange,
                          min: e.target.value ? parseFloat(e.target.value) : null
                        }
                      })}
                    />
                    <Input
                      type="number"
                      placeholder="Valor máximo"
                      value={filters.totalSpentRange.max || ""}
                      onChange={(e) => updateFilters({
                        totalSpentRange: {
                          ...filters.totalSpentRange,
                          max: e.target.value ? parseFloat(e.target.value) : null
                        }
                      })}
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Status do Cliente</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {statusOptions.map((status) => (
                      <div key={status.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`status-${status.id}`}
                          checked={filters.status.includes(status.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              updateFilters({ status: [...filters.status, status.id] });
                            } else {
                              updateFilters({
                                status: filters.status.filter(s => s !== status.id)
                              });
                            }
                          }}
                        />
                        <Label htmlFor={`status-${status.id}`} className="text-sm">
                          {status.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Clear Filters */}
                {activeFiltersCount > 0 && (
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="w-full"
                  >
                    <XIcon className="h-4 w-4 mr-2" />
                    Limpar Filtros
                  </Button>
                )}
              </div>
            </SheetContent>
          </Sheet>

          {/* Export Button */}
          {onExport && (
            <Button variant="outline" size="sm" onClick={onExport} className="h-10">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Filtros ativos:</span>

          {filters.searchTerm && (
            <Badge status="info" className="gap-1">
              Busca: {filters.searchTerm}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ searchTerm: "" })}
              />
            </Badge>
          )}

          {filters.location && (
            <Badge status="info" className="gap-1">
              Local: {filters.location}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ location: "" })}
              />
            </Badge>
          )}

          {filters.status.length > 0 && (
            <Badge status="info" className="gap-1">
              Status: {filters.status.length}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ status: [] })}
              />
            </Badge>
          )}

          {(filters.joinDateRange.from || filters.joinDateRange.to) && (
            <Badge status="info" className="gap-1">
              Período: {filters.joinDateRange.from && format(filters.joinDateRange.from, "dd/MM", { locale: ptBR })}
              {filters.joinDateRange.to && ` - ${format(filters.joinDateRange.to, "dd/MM", { locale: ptBR })}`}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ joinDateRange: { from: undefined, to: undefined } })}
              />
            </Badge>
          )}

          {(filters.totalSpentRange.min !== null || filters.totalSpentRange.max !== null) && (
            <Badge status="info" className="gap-1">
              Valor: {filters.totalSpentRange.min !== null ? `R$ ${filters.totalSpentRange.min}` : "R$ 0"}
              {filters.totalSpentRange.max !== null && ` - R$ ${filters.totalSpentRange.max}`}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ totalSpentRange: { min: null, max: null } })}
              />
            </Badge>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-6 px-2 text-xs"
          >
            Limpar todos
          </Button>
        </div>
      )}
    </div>
  );
}
