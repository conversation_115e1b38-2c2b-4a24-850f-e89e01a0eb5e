"use client";

import { Metric<PERSON>ard, MetricGrid } from "@saas/shared/components/MetricCard";
import {
  UsersIcon,
  TrendingUpIcon,
  DollarSignIcon,
  TargetIcon,
  ShoppingCartIcon,
} from "lucide-react";
import { useCustomerMetrics } from "../hooks/useCustomers";

interface CustomerMetricsProps {
  organizationId: string;
  period?: string;
}

export function CustomerMetrics({ organizationId, period = "30d" }: CustomerMetricsProps) {
  const { data: metrics, isLoading, error } = useCustomerMetrics(organizationId, period);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <MetricGrid columns={4}>
        {[1, 2, 3, 4].map((i) => (
          <MetricCard
            key={i}
            title="Carregando..."
            value="..."
            change="..."
            icon={UsersIcon}
            isLoading
          />
        ))}
      </MetricGrid>
    );
  }

  if (error || !metrics) {
    return (
      <MetricGrid columns={4}>
        <MetricCard
          title="Erro"
          value="Falha ao carregar"
          change="Tente novamente"
          icon={UsersIcon}
          isPositive={false}
        />
      </MetricGrid>
    );
  }

  const activePercentage = metrics.totalCustomers > 0
    ? ((metrics.activeCustomers / metrics.totalCustomers) * 100).toFixed(1)
    : "0";

  const metricsData = [
    {
      title: "Total de Clientes",
      value: metrics.totalCustomers.toLocaleString('pt-BR'),
      change: formatPercentage(metrics.customerGrowth),
      isPositive: metrics.customerGrowth >= 0,
      icon: UsersIcon,
      description: `Crescimento ${period}`,
      badge: {
        text: "Crescimento",
        variant: "default" as const,
      },
    },
    {
      title: "Clientes Ativos",
      value: metrics.activeCustomers.toLocaleString('pt-BR'),
      change: `${activePercentage}% do total`,
      isPositive: true,
      icon: TrendingUpIcon,
      description: "Engajados recentemente",
      badge: {
        text: "Ativos",
        variant: "outline" as const,
      },
    },
    {
      title: "Novos Clientes",
      value: metrics.newCustomers.toLocaleString('pt-BR'),
      change: `período ${period}`,
      isPositive: true,
      icon: TargetIcon,
      description: "Novos cadastros",
      badge: {
        text: "Novos",
        variant: "secondary" as const,
      },
    },
    {
      title: "Receita Total",
      value: formatCurrency(metrics.totalRevenueCents / 100),
      change: `Ticket médio: ${formatCurrency(metrics.averageOrderValueCents / 100)}`,
      isPositive: true,
      icon: ShoppingCartIcon,
      description: "Receita total",
      badge: {
        text: "Receita",
        variant: "outline" as const,
      },
    },
  ];

  return (
    <MetricGrid columns={4}>
      {metricsData.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          change={metric.change}
          isPositive={metric.isPositive}
          icon={metric.icon}
          description={metric.description}
          badge={metric.badge}
        />
      ))}
    </MetricGrid>
  );
}
