"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
  UserIcon,
  MailIcon,
  PhoneIcon,
  MapPinIcon,
  SaveIcon,
  XIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  UserCheckIcon,
} from "lucide-react";

interface Customer {
  id?: string;
  name: string;
  email: string;
  phone?: string;
  city?: string;
  state?: string;
  status: "active" | "inactive" | "pending" | "vip";
  image?: string;
  document?: string;
  birthDate?: string;
  address?: string;
  zipCode?: string;
  locale?: string;
  paymentsCustomerId?: string;
}

interface SimpleCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer?: Customer | null;
  onSave: (customer: Customer) => void;
  mode: 'create' | 'edit';
  organizationId?: string;
}

const statusOptions = [
  { id: 'active', name: 'Ativo', color: 'text-green-600', bgColor: 'bg-green-50' },
  { id: 'inactive', name: 'Inativo', color: 'text-red-600', bgColor: 'bg-red-50' },
  { id: 'pending', name: 'Pendente', color: 'text-yellow-600', bgColor: 'bg-yellow-50' },
  { id: 'vip', name: 'VIP', color: 'text-purple-600', bgColor: 'bg-purple-50' },
];

const states = [
  'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA',
  'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN',
  'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
];

export function SimpleCustomerModal({ isOpen, onClose, customer, onSave, mode, organizationId }: SimpleCustomerModalProps) {
  const [formData, setFormData] = useState<Customer>({
    name: '',
    email: '',
    phone: '',
    city: '',
    state: '',
    status: 'active',
    document: '',
    birthDate: '',
    address: '',
    zipCode: '',
    locale: 'pt-BR',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Preencher formulário quando editar
  useEffect(() => {
    if (customer && mode === 'edit') {
      setFormData(customer);
    } else {
      setFormData({
        name: '',
        email: '',
        phone: '',
        city: '',
        state: '',
        status: 'active',
        document: '',
        birthDate: '',
        address: '',
        zipCode: '',
        locale: 'pt-BR',
      });
    }
    setErrors({});
  }, [customer, mode, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (formData.phone && !/^\(\d{2}\)\s\d{4,5}-\d{4}$/.test(formData.phone)) {
      newErrors.phone = 'Formato: (11) 99999-9999';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof Customer, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePhoneChange = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 11) {
      const formatted = numbers.replace(/(\d{2})(\d{4,5})(\d{4})/, '($1) $2-$3');
      handleInputChange('phone', formatted);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      onSave({
        ...formData,
        organizationId: organizationId || '',
      });
      onClose();
    } catch (error) {
      console.error('Erro ao salvar cliente:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusConfig = (status: string) => {
    return statusOptions.find(option => option.id === status) || statusOptions[0];
  };

  const statusConfig = getStatusConfig(formData.status);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[600px] w-[600px] max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <UserIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <DialogTitle className="text-xl">
                  {mode === 'create' ? 'Novo Cliente' : 'Editar Cliente'}
                </DialogTitle>
                <p className="text-sm text-muted-foreground">
                  {mode === 'create'
                    ? 'Cadastre um novo cliente no sistema'
                    : 'Atualize as informações do cliente'
                  }
                </p>
              </div>
            </div>
            <Badge
              variant="outline"
              className={`${statusConfig.bgColor} ${statusConfig.color} border-current`}
            >
              {statusConfig.name}
            </Badge>
          </div>
        </DialogHeader>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Avatar e Nome */}
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={formData.image} />
              <AvatarFallback className="bg-primary/10 text-primary text-lg font-semibold">
                {formData.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <Label htmlFor="name" className="text-sm font-medium">Nome Completo *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Digite o nome completo"
                className={`mt-1 ${errors.name ? 'border-red-500' : ''}`}
              />
              {errors.name && (
                <p className="text-xs text-red-600 mt-1 flex items-center gap-1">
                  <AlertCircleIcon className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>
          </div>

          {/* Email e Telefone */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email" className="text-sm font-medium">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className={`mt-1 ${errors.email ? 'border-red-500' : ''}`}
              />
              {errors.email && (
                <p className="text-xs text-red-600 mt-1 flex items-center gap-1">
                  <AlertCircleIcon className="h-3 w-3" />
                  {errors.email}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="phone" className="text-sm font-medium">Telefone</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handlePhoneChange(e.target.value)}
                placeholder="(11) 99999-9999"
                className={`mt-1 ${errors.phone ? 'border-red-500' : ''}`}
              />
              {errors.phone && (
                <p className="text-xs text-red-600 mt-1 flex items-center gap-1">
                  <AlertCircleIcon className="h-3 w-3" />
                  {errors.phone}
                </p>
              )}
            </div>
          </div>

          {/* CPF/CNPJ e Nascimento */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="document" className="text-sm font-medium">CPF/CNPJ</Label>
              <Input
                id="document"
                value={formData.document}
                onChange={(e) => handleInputChange('document', e.target.value)}
                placeholder="000.000.000-00"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="birthDate" className="text-sm font-medium">Data de Nascimento</Label>
              <Input
                id="birthDate"
                type="date"
                value={formData.birthDate}
                onChange={(e) => handleInputChange('birthDate', e.target.value)}
                className="mt-1"
              />
            </div>
          </div>

          {/* Endereço - CEP primeiro */}
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-3">
              <div>
                <Label htmlFor="zipCode" className="text-sm font-medium">CEP</Label>
                <Input
                  id="zipCode"
                  value={formData.zipCode}
                  onChange={(e) => handleInputChange('zipCode', e.target.value)}
                  placeholder="00000-000"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="city" className="text-sm font-medium">Cidade</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="São Paulo"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="state" className="text-sm font-medium">Estado</Label>
                <Select
                  value={formData.state}
                  onValueChange={(value) => handleInputChange('state', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="UF" />
                  </SelectTrigger>
                  <SelectContent>
                    {states.map((state) => (
                      <SelectItem key={state} value={state}>
                        {state}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="address" className="text-sm font-medium">Endereço Completo</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Rua, número, bairro"
                className="mt-1"
              />
            </div>
          </div>

          {/* Status */}
          <div>
            <Label className="text-sm font-medium">Status do Cliente *</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange('status', value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((status) => (
                  <SelectItem key={status.id} value={status.id}>
                    {status.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Botões */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              <XIcon className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Salvando...
                </>
              ) : (
                <>
                  <SaveIcon className="h-4 w-4 mr-2" />
                  {mode === 'create' ? 'Criar Cliente' : 'Salvar Alterações'}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
