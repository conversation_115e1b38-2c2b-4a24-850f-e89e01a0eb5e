"use client";

import { useState } from "react";
import { DataTable, DataTableColumn, DataTableAction } from "@saas/shared/components/DataTable";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { CustomerDetailsSheet } from "./CustomerDetailsSheet";
import { useCustomerForm } from "../hooks/useCustomerForm";
import { useCustomers, Customer, CustomerFilterState } from "../hooks/useCustomers";
import {
  EyeIcon,
  EditIcon,
  Trash2Icon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  StarIcon,
} from "lucide-react";

interface CustomersTableProps {
  organizationId: string;
  filters?: CustomerFilterState;
}

const statusColors = {
  active: "bg-green-100 text-green-800 border-green-200",
  inactive: "bg-gray-100 text-gray-800 border-gray-200",
};

const statusLabels = {
  active: "Ativo",
  inactive: "Inativo",
};

export function CustomersTable({ organizationId, filters }: CustomersTableProps) {
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const { openForm } = useCustomerForm();

  const { data, isLoading, error } = useCustomers(organizationId, filters);

  const customers = data?.customers || [];
  const pagination = data?.pagination || null;


  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const formatDate = (date: Date | string) => {
    if (!date) return "N/A";

    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return "Data inválida";

    return new Intl.DateTimeFormat("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(dateObj);
  };

  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsDetailsOpen(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    openForm("edit", customer);
  };

  const handleDeleteCustomer = (customer: Customer) => {
    // TODO: Implementar exclusão
    console.log("Delete customer:", customer.id);
  };

  const columns: DataTableColumn<Customer>[] = [
    {
      key: "customer",
      title: "Cliente",
      render: (customer) => {
        if (!customer) return null;

        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={customer.image || ""} />
              <AvatarFallback className="text-xs">
                {customer.name
                  ?.split(" ")
                  .map((n) => n[0])
                  .join("")
                  .toUpperCase() || "U"}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium text-sm">{customer.name || "Nome não informado"}</div>
              <div className="text-xs text-muted-foreground">{customer.email || "Email não informado"}</div>
            </div>
          </div>
        );
      },
    },
    {
      key: "status",
      title: "Status",
      render: (customer) => {
        if (!customer) return null;

        return (
          <Badge
            variant="outline"
            className={statusColors[customer.status] || statusColors.inactive}
          >
            {statusLabels[customer.status] || "Desconhecido"}
          </Badge>
        );
      },
    },
    {
      key: "totalSpent",
      title: "Total Gasto",
      render: (customer) => {
        if (!customer) return null;

        return (
          <div className="text-sm font-medium">
            {formatCurrency((customer.totalSpent || 0) / 100)}
          </div>
        );
      },
    },
    {
      key: "purchaseCount",
      title: "Compras",
      render: (customer) => {
        if (!customer) return null;

        return (
          <div className="text-sm">{customer.purchaseCount || 0}</div>
        );
      },
    },
    {
      key: "lastPurchase",
      title: "Última Compra",
      render: (customer) => {
        if (!customer) return null;

        return (
          <div className="text-sm">
            {customer.lastPurchase ? formatDate(customer.lastPurchase) : "Nunca"}
          </div>
        );
      },
    },
    {
      key: "createdAt",
      title: "Cadastrado em",
      render: (customer) => {
        if (!customer) return null;

        return (
          <div className="text-sm">{formatDate(customer.createdAt)}</div>
        );
      },
    },
  ];

  const actions: DataTableAction<Customer>[] = [
    {
      label: "Ver detalhes",
      icon: EyeIcon,
      onClick: handleViewCustomer,
    },
    {
      label: "Editar",
      icon: EditIcon,
      onClick: handleEditCustomer,
    },
    {
      label: "Excluir",
      icon: Trash2Icon,
      onClick: handleDeleteCustomer,
      variant: "destructive",
    },
  ];

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Erro ao carregar clientes: {error.message || 'Erro desconhecido'}</p>
        <Button
          onClick={() => window.location.reload()}
          variant="outline"
          className="mt-4"
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  return (
    <>
      <DataTable
        data={customers}
        columns={columns}
        actions={actions}
        isLoading={isLoading}
        emptyMessage="Nenhum cliente encontrado"
      />

      <CustomerDetailsSheet
        customer={selectedCustomer}
        isOpen={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
      />
    </>
  );
}
