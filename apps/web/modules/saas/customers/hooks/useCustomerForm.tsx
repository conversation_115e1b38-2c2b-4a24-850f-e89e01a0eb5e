"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface Customer {
  id?: string;
  name: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  status: "active" | "inactive" | "pending" | "vip";
  avatar?: string;
  tags: string[];
  notes?: string;
  birthDate?: string;
  document?: string;
  address?: string;
  zipCode?: string;
  company?: string;
  position?: string;
}

interface CustomerFormContextType {
  isFormOpen: boolean;
  selectedCustomer: Customer | null;
  mode: 'create' | 'edit';
  openCreateForm: () => void;
  openEditForm: (customer: Customer) => void;
  closeForm: () => void;
  handleSave: (customer: Customer) => void;
}

const CustomerFormContext = createContext<CustomerFormContextType | undefined>(undefined);

export function useCustomerForm() {
  const context = useContext(CustomerFormContext);
  if (context === undefined) {
    throw new Error('useCustomerForm must be used within a CustomerFormProvider');
  }
  return context;
}

interface CustomerFormProviderProps {
  children: ReactNode;
}

export function CustomerFormProvider({ children }: CustomerFormProviderProps) {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [mode, setMode] = useState<'create' | 'edit'>('create');

  const openCreateForm = () => {
    setSelectedCustomer(null);
    setMode('create');
    setIsFormOpen(true);
  };

  const openEditForm = (customer: Customer) => {
    setSelectedCustomer(customer);
    setMode('edit');
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setSelectedCustomer(null);
  };

  const handleSave = (customer: Customer) => {
    if (mode === 'create') {
      console.log('Criando cliente:', customer);
      // Aqui você faria a chamada para a API de criação
    } else {
      console.log('Editando cliente:', customer);
      // Aqui você faria a chamada para a API de edição
    }

    // Simular sucesso
    setTimeout(() => {
      closeForm();
      // Aqui você poderia atualizar a lista de clientes
    }, 1000);
  };

  // Garantir que os dados tenham valores padrão seguros
  const getSafeCustomer = (customer: Customer | null): Customer | null => {
    if (!customer) return null;

    return {
      ...customer,
      tags: customer.tags || [],
      notes: customer.notes || '',
      birthDate: customer.birthDate || '',
      document: customer.document || '',
      address: customer.address || '',
      zipCode: customer.zipCode || '',
      company: customer.company || '',
      position: customer.position || '',
    };
  };

  const value: CustomerFormContextType = {
    isFormOpen,
    selectedCustomer: getSafeCustomer(selectedCustomer),
    mode,
    openCreateForm,
    openEditForm,
    closeForm,
    handleSave,
  };

  return (
    <CustomerFormContext.Provider value={value}>
      {children}
    </CustomerFormContext.Provider>
  );
}
