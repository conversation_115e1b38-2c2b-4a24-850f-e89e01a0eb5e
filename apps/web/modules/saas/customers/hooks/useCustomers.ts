"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@shared/lib/api-client";

export interface Customer {
  id: string;
  name: string;
  email: string;
  image: string | null;
  createdAt: string;
  updatedAt: string;
  status: string;
  paymentsCustomerId: string | null;
  locale: string | null;
  totalSpent: number;
  lastPurchase: string | null;
  purchaseCount: number;
}

export interface CustomerMetrics {
  totalCustomers: number;
  activeCustomers: number;
  inactiveCustomers: number;
  newCustomers: number;
  totalRevenueCents: number;
  averageOrderValueCents: number;
  customerGrowth: number;
  period: string;
}

export interface CustomersResponse {
  customers: Customer[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface CustomerFilters {
  search?: string;
  status?: string[];
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  page?: number;
  limit?: number;
}

// Interface para compatibilidade com CustomerFilterState
export interface CustomerFilterState {
  searchTerm: string;
  status: string[];
  totalSpentRange: { min: number | null; max: number | null };
  location: string;
  joinDateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
}

// Função para converter CustomerFilterState para CustomerFilters
export function convertFiltersToApiFormat(filters: CustomerFilterState): CustomerFilters {
  return {
    search: filters.searchTerm || undefined,
    status: filters.status.length > 0 ? filters.status : undefined,
    sortBy: "createdAt",
    sortOrder: "desc",
  };
}

export function useCustomers(organizationId: string, filters: CustomerFilters = {}, page: number = 1, limit: number = 10) {
  return useQuery({
    queryKey: ["customers", organizationId, filters, page, limit],
    queryFn: async () => {
      const response = await apiClient.customers.$get({
        query: {
          organizationId,
          page: page.toString(),
          limit: limit.toString(),
          search: filters.search || "",
          status: filters.status?.join(",") || "",
          sortBy: filters.sortBy || "createdAt",
          sortOrder: filters.sortOrder || "desc",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch customers");
      }

      return response.json();
    },
    enabled: !!organizationId,
  });
}

export function useCustomerMetrics(organizationId: string, period: string = "30d") {
  return useQuery({
    queryKey: ["customerMetrics", organizationId, period],
    queryFn: async () => {
      const response = await apiClient.customers.metrics.$get({
        query: {
          organizationId,
          period: period as "7d" | "30d" | "90d" | "1y",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch customer metrics");
      }

      return response.json();
    },
    enabled: !!organizationId,
  });
}

export function useCreateCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (customerData: {
      organizationId: string;
      name: string;
      email: string;
      phone?: string;
      city?: string;
      state?: string;
      document?: string;
      birthDate?: string;
      address?: string;
      zipCode?: string;
      locale?: string;
    }) => {
      console.log('Creating customer with data:', customerData);

      const response = await apiClient.customers.$post({
        json: customerData,
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Failed to create customer:', errorData);
        throw new Error(`Failed to create customer: ${response.status}`);
      }

      const result = await response.json();
      console.log('Customer created successfully:', result);
      return result;
    },
    onSuccess: (data, variables) => {
      console.log('Invalidating queries for organization:', variables.organizationId);

      // Invalidate customers list
      queryClient.invalidateQueries({
        queryKey: ["customers", variables.organizationId],
      });

      // Invalidate customer metrics
      queryClient.invalidateQueries({
        queryKey: ["customerMetrics", variables.organizationId],
      });
    },
    onError: (error) => {
      console.error('Error creating customer:', error);
    },
  });
}
