'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export interface Testimonial {
  id: string;
  organizationId: string;
  customerName: string;
  customerEmail?: string;
  customerPhoto?: string;
  customerRole?: string;
  customerLocation?: string;
  title?: string;
  content: string;
  rating: number;
  source: 'WHATSAPP' | 'FACEBOOK' | 'TIKTOK' | 'INSTAGRAM' | 'EMAIL' | 'MANUAL';
  sourceUrl?: string;
  sourceDate?: string;
  attachments: string[];
  isApproved: boolean;
  isActive: boolean;
  isFeatured: boolean;
  approvedBy?: string;
  approvedAt?: string;
  productId?: string;
  product?: {
    id: string;
    name: string;
    slug: string;
  };
  tags: string[];
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface TestimonialsResponse {
  testimonials: Testimonial[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface TestimonialsFilters {
  page?: number;
  limit?: number;
  productId?: string;
  isApproved?: boolean;
  isActive?: boolean;
  isFeatured?: boolean;
  search?: string;
}

export interface CreateTestimonialData {
  customerName: string;
  customerEmail?: string;
  customerPhoto?: string;
  customerRole?: string;
  customerLocation?: string;
  title?: string;
  content: string;
  rating: number;
  source: 'WHATSAPP' | 'FACEBOOK' | 'TIKTOK' | 'INSTAGRAM' | 'EMAIL' | 'MANUAL';
  sourceUrl?: string;
  sourceDate?: string;
  attachments?: string[];
  productId?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface UpdateTestimonialData extends Partial<CreateTestimonialData> {
  id: string;
  isApproved?: boolean;
  isActive?: boolean;
  isFeatured?: boolean;
}

class TestimonialsApiClient {
  private baseUrl = '/api/testimonials';

  async getTestimonials(filters: TestimonialsFilters = {}): Promise<TestimonialsResponse> {
    const params = new URLSearchParams();

    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.productId) params.append('productId', filters.productId);
    if (filters.isApproved !== undefined) params.append('isApproved', filters.isApproved.toString());
    if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
    if (filters.isFeatured !== undefined) params.append('isFeatured', filters.isFeatured.toString());
    if (filters.search) params.append('search', filters.search);

    const response = await fetch(`${this.baseUrl}?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch testimonials');
    }
    return response.json();
  }

  async getTestimonial(id: string): Promise<{ testimonial: Testimonial }> {
    const response = await fetch(`${this.baseUrl}/${id}`);
    if (!response.ok) {
      throw new Error('Failed to fetch testimonial');
    }
    return response.json();
  }

  async createTestimonial(data: CreateTestimonialData): Promise<{ testimonial: Testimonial }> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create testimonial');
    }

    return response.json();
  }

  async updateTestimonial(data: UpdateTestimonialData): Promise<{ testimonial: Testimonial }> {
    const { id, ...updateData } = data;
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update testimonial');
    }

    return response.json();
  }

  async deleteTestimonial(id: string): Promise<{ message: string }> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete testimonial');
    }

    return response.json();
  }

  async getPublicTestimonials(productId: string): Promise<{ testimonials: Testimonial[] }> {
    const response = await fetch(`${this.baseUrl}/public/${productId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch public testimonials');
    }
    return response.json();
  }
}

const apiClient = new TestimonialsApiClient();

// React Query Hooks
export function useTestimonials(filters: TestimonialsFilters = {}) {
  return useQuery({
    queryKey: ['testimonials', 'list', filters],
    queryFn: () => apiClient.getTestimonials(filters),
  });
}

export function useTestimonial(id: string) {
  return useQuery({
    queryKey: ['testimonials', 'detail', id],
    queryFn: () => apiClient.getTestimonial(id),
    enabled: !!id,
  });
}

export function usePublicTestimonials(productId: string) {
  return useQuery({
    queryKey: ['testimonials', 'public', productId],
    queryFn: () => apiClient.getPublicTestimonials(productId),
    enabled: !!productId,
  });
}

export function useCreateTestimonial() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.createTestimonial,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['testimonials'] });
    },
  });
}

export function useUpdateTestimonial() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.updateTestimonial,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['testimonials'] });
      queryClient.setQueryData(['testimonials', 'detail', data.testimonial.id], data);
    },
  });
}

export function useDeleteTestimonial() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.deleteTestimonial,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['testimonials'] });
    },
  });
}

// Hook para gerenciar estado local dos filtros
export function useTestimonialsFilters() {
  const [filters, setFilters] = useState<TestimonialsFilters>({
    page: 1,
    limit: 20,
  });

  const updateFilter = (key: keyof TestimonialsFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      // Reset page when changing other filters
      ...(key !== 'page' && { page: 1 }),
    }));
  };

  const resetFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
    });
  };

  return {
    filters,
    updateFilter,
    resetFilters,
  };
}
