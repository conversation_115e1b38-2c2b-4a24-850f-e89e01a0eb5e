"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Label } from "@ui/components/label";
import { Checkbox } from "@ui/components/checkbox";
import { Separator } from "@ui/components/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@ui/components/sheet";
import {
  FilterIcon,
  SearchIcon,
  XIcon,
  RefreshCwIcon,
  CalendarIcon,
  DollarSignIcon,
  UsersIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ArchiveIcon,
  EyeIcon,
  EyeOffIcon,
  StarIcon,
  TrendingUpIcon,
  PackageIcon,
  TagIcon,
  Globe,
} from "lucide-react";

export interface FilterOption {
  id: string;
  name: string;
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
  bgColor?: string;
  count?: number;
}

export interface FilterConfig {
  type: 'checkbox' | 'select' | 'input' | 'range' | 'date' | 'switch';
  key: string;
  label: string;
  placeholder?: string;
  options?: FilterOption[];
  min?: number;
  max?: number;
  step?: number;
  icon?: React.ComponentType<{ className?: string }>;
}

export interface AdvancedFilters {
  [key: string]: any;
}

interface AdvancedFiltersSheetProps {
  onFiltersChange: (filters: AdvancedFilters) => void;
  activeFilters: AdvancedFilters;
  filterConfigs: FilterConfig[];
  totalItems?: number;
  loading?: boolean;
  title?: string;
  description?: string;
  triggerLabel?: string;
  showSearch?: boolean;
  searchPlaceholder?: string;
  onClearFilters?: () => void;
  onResetFilters?: () => void;
}

export function AdvancedFiltersSheet({
  onFiltersChange,
  activeFilters,
  filterConfigs,
  totalItems = 0,
  loading = false,
  title = "Filtros Avançados",
  description,
  triggerLabel = "Filtros",
  showSearch = true,
  searchPlaceholder = "Buscar...",
  onClearFilters,
  onResetFilters,
}: AdvancedFiltersSheetProps) {
  const [filters, setFilters] = useState<AdvancedFilters>(activeFilters);
  const [isOpen, setIsOpen] = useState(false);
  const [localSearchTerm, setLocalSearchTerm] = useState(activeFilters.searchTerm || "");

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleArrayToggle = (key: string, value: string) => {
    const currentArray = filters[key] || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter((item: string) => item !== value)
      : [...currentArray, value];
    handleFilterChange(key, newArray);
  };

  const handleRangeChange = (key: string, field: 'min' | 'max', value: number | null) => {
    const currentRange = filters[key] || { min: null, max: null };
    const newRange = { ...currentRange, [field]: value };
    handleFilterChange(key, newRange);
  };

  const handleClearFilters = () => {
    const clearedFilters: AdvancedFilters = {};
    filterConfigs.forEach(config => {
      if (config.type === 'range') {
        clearedFilters[config.key] = { min: null, max: null };
      } else if (config.type === 'checkbox' || config.type === 'select') {
        clearedFilters[config.key] = [];
      } else {
        clearedFilters[config.key] = '';
      }
    });
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
    onClearFilters?.();
  };

  const handleResetFilters = () => {
    setFilters(activeFilters);
    onResetFilters?.();
  };

  const activeFiltersCount = useMemo(() => {
    return Object.entries(filters).reduce((count, [key, value]) => {
      if (Array.isArray(value)) {
        return count + (value.length > 0 ? 1 : 0);
      } else if (typeof value === 'object' && value !== null) {
        return count + (Object.values(value).some(v => v !== null && v !== '') ? 1 : 0);
      } else {
        return count + (value !== '' && value !== null ? 1 : 0);
      }
    }, 0);
  }, [filters]);

  const renderFilter = (config: FilterConfig) => {
    const value = filters[config.key];

    switch (config.type) {
      case 'checkbox':
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              {config.icon && <config.icon className="h-4 w-4" />}
              {config.label}
            </Label>
            <div className="space-y-2">
              {config.options?.map((option) => {
                const Icon = option.icon;
                const isChecked = value?.includes(option.id) || false;
                return (
                  <div key={option.id} className="flex items-center space-x-3">
                    <Checkbox
                      id={`${config.key}-${option.id}`}
                      checked={isChecked}
                      onCheckedChange={() => handleArrayToggle(config.key, option.id)}
                    />
                    <Label
                      htmlFor={`${config.key}-${option.id}`}
                      className="flex items-center gap-2 text-sm cursor-pointer flex-1"
                    >
                      {Icon && (
                        <Icon
                          className={`h-4 w-4 ${
                            option.color || "text-muted-foreground"
                          }`}
                        />
                      )}
                      <span>{option.name}</span>
                      {option.count !== undefined && (
                        <Badge variant="secondary" className="text-xs">
                          {option.count}
                        </Badge>
                      )}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>
        );

      case 'select':
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              {config.icon && <config.icon className="h-4 w-4" />}
              {config.label}
            </Label>
            <Select
              value={value || ""}
              onValueChange={(newValue) => handleFilterChange(config.key, newValue)}
            >
              <SelectTrigger>
                <SelectValue placeholder={config.placeholder || `Selecione ${config.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {config.options?.map((option) => (
                  <SelectItem key={option.id} value={option.id}>
                    <div className="flex items-center gap-2">
                      {option.icon && <option.icon className="h-4 w-4" />}
                      <span>{option.name}</span>
                      {option.count !== undefined && (
                        <Badge variant="secondary" className="text-xs ml-auto">
                          {option.count}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'input':
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              {config.icon && <config.icon className="h-4 w-4" />}
              {config.label}
            </Label>
            <Input
              type="text"
              placeholder={config.placeholder || `Digite ${config.label.toLowerCase()}`}
              value={value || ""}
              onChange={(e) => handleFilterChange(config.key, e.target.value)}
            />
          </div>
        );

      case 'range':
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              {config.icon && <config.icon className="h-4 w-4" />}
              {config.label}
            </Label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor={`${config.key}-min`} className="text-xs text-muted-foreground">
                  Mínimo
                </Label>
                <Input
                  id={`${config.key}-min`}
                  type="number"
                  placeholder="0"
                  value={value?.min || ""}
                  onChange={(e) => handleRangeChange(config.key, 'min', e.target.value ? Number(e.target.value) : null)}
                  min={config.min}
                  max={config.max}
                  step={config.step}
                />
              </div>
              <div>
                <Label htmlFor={`${config.key}-max`} className="text-xs text-muted-foreground">
                  Máximo
                </Label>
                <Input
                  id={`${config.key}-max`}
                  type="number"
                  placeholder="∞"
                  value={value?.max || ""}
                  onChange={(e) => handleRangeChange(config.key, 'max', e.target.value ? Number(e.target.value) : null)}
                  min={config.min}
                  max={config.max}
                  step={config.step}
                />
              </div>
            </div>
          </div>
        );

      case 'switch':
        return (
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium flex items-center gap-2">
              {config.icon && <config.icon className="h-4 w-4" />}
              {config.label}
            </Label>
            <Switch
              checked={value || false}
              onCheckedChange={(checked) => handleFilterChange(config.key, checked)}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <FilterIcon className="h-4 w-4 mr-2" />
          {triggerLabel}
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[450px] sm:w-[600px] overflow-y-auto border-l border-border/50">
        <SheetHeader className="sticky top-0 bg-background z-10 pb-4 border-b border-border/50">
          <SheetTitle className="flex items-center gap-2 text-foreground">
            <FilterIcon className="h-5 w-5 text-primary" />
            {title}
          </SheetTitle>
          <SheetDescription className="text-muted-foreground">
            {description || (totalItems > 0 ? `${totalItems} itens encontrados` : "Aplique filtros para encontrar itens específicos")}
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 mt-6 pb-20">
          {/* Barra de Busca */}
          {showSearch && (
            <div className="space-y-3">
              <Label htmlFor="search" className="text-sm font-medium text-foreground">
                Buscar
              </Label>
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  type="text"
                  placeholder={searchPlaceholder}
                  value={localSearchTerm}
                  onChange={(e) => {
                    setLocalSearchTerm(e.target.value);
                    handleFilterChange('searchTerm', e.target.value);
                  }}
                  className="pl-10"
                />
                {localSearchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    onClick={() => {
                      setLocalSearchTerm("");
                      handleFilterChange('searchTerm', "");
                    }}
                  >
                    <XIcon className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Filtros */}
          <div className="space-y-6">
            {filterConfigs.map((config, index) => (
              <div key={config.key}>
                {renderFilter(config)}
                {index < filterConfigs.length - 1 && <Separator className="mt-6" />}
              </div>
            ))}
          </div>

          {/* Ações */}
          <div className="flex gap-3 pt-6 border-t">
            <Button
              variant="outline"
              onClick={handleClearFilters}
              className="flex-1"
            >
              <XIcon className="h-4 w-4 mr-2" />
              Limpar
            </Button>
            {onResetFilters && (
              <Button
                variant="outline"
                onClick={handleResetFilters}
                className="flex-1"
              >
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Resetar
              </Button>
            )}
            <Button
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Aplicar
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
