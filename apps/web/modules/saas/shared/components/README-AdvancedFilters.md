# AdvancedFiltersSheet - Componente de Filtros Reutilizável

## 📋 Visão Geral

O `AdvancedFiltersSheet` é um componente reutilizável para criar filtros avançados em sheets, mantendo consistência visual e funcional em toda a aplicação.

## 🚀 Características

- ✅ **Reutilizável**: Use em qualquer página que precise de filtros
- ✅ **Customizável**: Configure filtros específicos para cada contexto
- ✅ **Consistente**: Mantém o mesmo padrão visual em toda a aplicação
- ✅ **Flexível**: Suporta múltiplos tipos de filtros
- ✅ **Responsivo**: Adapta-se a diferentes tamanhos de tela
- ✅ **Acessível**: Segue padrões de acessibilidade

## 🎯 Tipos de Filtros Suportados

### 1. **Checkbox** - Seleção múltipla
```typescript
{
  type: 'checkbox',
  key: 'status',
  label: 'Status',
  icon: CheckCircleIcon,
  options: [
    { id: 'active', name: 'Ativo', icon: CheckCircleIcon, color: 'text-green-600', count: 892 },
    { id: 'inactive', name: 'Inativo', icon: XCircleIcon, color: 'text-red-600', count: 156 },
  ]
}
```

### 2. **Select** - Seleção única
```typescript
{
  type: 'select',
  key: 'category',
  label: 'Categoria',
  icon: PackageIcon,
  placeholder: 'Selecione uma categoria',
  options: [
    { id: 'course', name: 'Curso', count: 12 },
    { id: 'ebook', name: 'E-book', count: 8 },
  ]
}
```

### 3. **Input** - Campo de texto
```typescript
{
  type: 'input',
  key: 'search',
  label: 'Buscar',
  icon: SearchIcon,
  placeholder: 'Digite para buscar...'
}
```

### 4. **Range** - Faixa de valores
```typescript
{
  type: 'range',
  key: 'priceRange',
  label: 'Faixa de Preço',
  icon: DollarSignIcon,
  min: 0,
  max: 1000,
  step: 10
}
```

### 5. **Switch** - Toggle
```typescript
{
  type: 'switch',
  key: 'isActive',
  label: 'Apenas Ativos',
  icon: CheckCircleIcon
}
```

## 🔧 Como Usar

### 1. **Importar o componente e hook**
```typescript
import { AdvancedFiltersSheet, FilterConfig } from "@saas/shared/components/AdvancedFiltersSheet";
import { useAdvancedFilters } from "@saas/shared/hooks/useAdvancedFilters";
```

### 2. **Configurar os filtros**
```typescript
const { filters, updateFilters, clearFilters } = useAdvancedFilters({
  initialFilters: {
    searchTerm: "",
    status: [],
    priceRange: { min: null, max: null },
  },
  onFiltersChange: (newFilters) => {
    // Implementar lógica de filtros
    console.log("Filtros aplicados:", newFilters);
  }
});
```

### 3. **Definir configuração dos filtros**
```typescript
const filterConfigs: FilterConfig[] = [
  {
    type: 'checkbox',
    key: 'status',
    label: 'Status',
    icon: CheckCircleIcon,
    options: [
      { id: 'active', name: 'Ativo', icon: CheckCircleIcon, color: 'text-green-600', count: 892 },
      { id: 'inactive', name: 'Inativo', icon: XCircleIcon, color: 'text-red-600', count: 156 },
    ]
  },
  {
    type: 'range',
    key: 'priceRange',
    label: 'Faixa de Preço',
    icon: DollarSignIcon,
    min: 0,
    max: 1000,
    step: 10
  }
];
```

### 4. **Usar no ActionBar**
```typescript
<ActionBar
  searchValue={filters.searchTerm || ""}
  onSearchChange={(value) => updateFilters({ ...filters, searchTerm: value })}
  searchPlaceholder="Buscar..."
  actions={
    <>
      <AdvancedFiltersSheet
        onFiltersChange={updateFilters}
        activeFilters={filters}
        filterConfigs={filterConfigs}
        totalItems={filteredItems.length}
        title="Filtros Avançados"
        description="Aplique filtros para encontrar itens específicos"
        triggerLabel="Filtros"
        onClearFilters={clearFilters}
      />
    </>
  }
/>
```

## 📝 Exemplos de Uso

### **Clientes**
- Status (Ativo, Inativo, Pendente, VIP)
- Faixa de valor gasto
- Localização
- Data de cadastro

### **Vendas**
- Status da venda
- Método de pagamento
- Faixa de valor
- Data da venda
- Tipo de cliente

### **Produtos**
- Status (Publicado, Rascunho, Arquivado)
- Categoria
- Faixa de preço
- Visibilidade
- Idioma

## 🎨 Customização

### **Props Disponíveis**
```typescript
interface AdvancedFiltersSheetProps {
  onFiltersChange: (filters: AdvancedFilters) => void;
  activeFilters: AdvancedFilters;
  filterConfigs: FilterConfig[];
  totalItems?: number;
  loading?: boolean;
  title?: string;
  description?: string;
  triggerLabel?: string;
  showSearch?: boolean;
  searchPlaceholder?: string;
  onClearFilters?: () => void;
  onResetFilters?: () => void;
}
```

### **Estilização**
O componente usa as classes do Tailwind CSS e segue o design system da aplicação. Para customizar:

1. **Cores**: Use as classes de cor do Tailwind
2. **Ícones**: Use ícones do Lucide React
3. **Tamanhos**: O sheet é responsivo (450px em mobile, 600px em desktop)

## 🔄 Integração com DataTable

O componente se integra perfeitamente com o `DataTable`:

```typescript
// Filtrar dados baseado nos filtros
const filteredData = data.filter(item => {
  // Implementar lógica de filtros
  if (filters.status && filters.status.length > 0) {
    if (!filters.status.includes(item.status)) return false;
  }

  if (filters.priceRange) {
    const { min, max } = filters.priceRange;
    if (min !== null && item.price < min) return false;
    if (max !== null && item.price > max) return false;
  }

  return true;
});
```

## 🚀 Benefícios

1. **Consistência**: Mesmo padrão visual em todas as páginas
2. **Reutilização**: Um componente para todos os filtros
3. **Manutenibilidade**: Mudanças centralizadas
4. **Performance**: Hook otimizado para gerenciamento de estado
5. **Acessibilidade**: Segue padrões WCAG
6. **Responsividade**: Funciona em todos os dispositivos

## 📚 Próximos Passos

1. Implementar em todas as páginas que precisam de filtros
2. Adicionar mais tipos de filtros conforme necessário
3. Criar testes unitários
4. Documentar casos de uso específicos
