"use client";

import { useState, useCallback, useMemo } from "react";
import { AdvancedFilters } from "../components/AdvancedFiltersSheet";

interface UseAdvancedFiltersProps {
  initialFilters?: AdvancedFilters;
  onFiltersChange?: (filters: AdvancedFilters) => void;
}

export function useAdvancedFilters({
  initialFilters = {},
  onFiltersChange
}: UseAdvancedFiltersProps = {}) {
  const [filters, setFilters] = useState<AdvancedFilters>(initialFilters);

  const updateFilters = useCallback((newFilters: AdvancedFilters) => {
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  }, [onFiltersChange]);

  const clearFilters = useCallback(() => {
    const clearedFilters: AdvancedFilters = {};
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  }, [onFiltersChange]);

  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
    onFiltersChange?.(initialFilters);
  }, [initialFilters, onFiltersChange]);

  const activeFiltersCount = useMemo(() => {
    return Object.entries(filters).reduce((count, [key, value]) => {
      if (Array.isArray(value)) {
        return count + (value.length > 0 ? 1 : 0);
      } else if (typeof value === 'object' && value !== null) {
        return count + (Object.values(value).some(v => v !== null && v !== '') ? 1 : 0);
      } else {
        return count + (value !== '' && value !== null ? 1 : 0);
      }
    }, 0);
  }, [filters]);

  const hasActiveFilters = activeFiltersCount > 0;

  return {
    filters,
    updateFilters,
    clearFilters,
    resetFilters,
    activeFiltersCount,
    hasActiveFilters,
  };
}
