"use client";

import { useMemo } from "react";

export interface MetricData {
  total: number;
  change: number;
  monthly?: number;
  monthlyChange?: number;
  today?: number;
  todayChange?: number;
  active?: number;
  activeChange?: number;
  published?: number;
  publishedChange?: number;
}

export interface MetricsData {
  revenue: MetricData;
  sales: MetricData;
  customers: MetricData;
  products: MetricData;
  conversion: {
    rate: number;
    change: number;
  };
  performance: {
    score: number;
    change: number;
  };
}

export function useMetrics(organizationId: string) {
  // Mock data - replace with real API calls
  const mockData: MetricsData = {
    revenue: {
      total: 12543050, // in cents
      change: 12.5,
      monthly: 4523080,
      monthlyChange: 8.2,
    },
    sales: {
      total: 2847,
      change: 15.3,
      today: 23,
      todayChange: 5.1,
    },
    customers: {
      total: 1371,
      change: 8.7,
      active: 892,
      activeChange: 12.1,
    },
    products: {
      total: 45,
      change: 3.2,
      published: 38,
      publishedChange: 6.8,
    },
    conversion: {
      rate: 3.2,
      change: 0.8,
    },
    performance: {
      score: 87,
      change: 2.3,
    },
  };

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(cents / 100);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("pt-BR").format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const formatChange = (change: number) => {
    return `${change > 0 ? '+' : ''}${change.toFixed(1)}%`;
  };

  const metrics = useMemo(() => {
    if (!mockData) return null;

    return {
      revenue: {
        title: "Receita Total",
        value: formatCurrency(mockData.revenue.total),
        change: formatChange(mockData.revenue.change),
        isPositive: mockData.revenue.change > 0,
        description: `Este mês: ${formatCurrency(mockData.revenue.monthly)}`,
        badge: {
          text: "Mês atual",
          variant: "secondary" as const,
        },
      },
      sales: {
        title: "Vendas",
        value: formatNumber(mockData.sales.total),
        change: formatChange(mockData.sales.change),
        isPositive: mockData.sales.change > 0,
        description: `Hoje: ${formatNumber(mockData.sales.today)} vendas`,
        badge: {
          text: "Hoje",
          variant: "default" as const,
        },
      },
      customers: {
        title: "Clientes",
        value: formatNumber(mockData.customers.total),
        change: formatChange(mockData.customers.change),
        isPositive: mockData.customers.change > 0,
        description: `${formatNumber(mockData.customers.active)} ativos`,
        badge: {
          text: "Ativos",
          variant: "outline" as const,
        },
      },
      products: {
        title: "Produtos",
        value: formatNumber(mockData.products.total),
        change: formatChange(mockData.products.change),
        isPositive: mockData.products.change > 0,
        description: `${formatNumber(mockData.products.published)} publicados`,
        badge: {
          text: "Publicados",
          variant: "secondary" as const,
        },
      },
      conversion: {
        title: "Taxa de Conversão",
        value: formatPercentage(mockData.conversion.rate),
        change: formatChange(mockData.conversion.change),
        isPositive: mockData.conversion.change > 0,
        description: "Visitantes que compram",
        badge: {
          text: "Performance",
          variant: "default" as const,
        },
      },
      performance: {
        title: "Score de Performance",
        value: `${mockData.performance.score}/100`,
        change: `${mockData.performance.change > 0 ? '+' : ''}${mockData.performance.change.toFixed(1)}`,
        isPositive: mockData.performance.change > 0,
        description: "Baseado em múltiplas métricas",
        badge: {
          text: "Score",
          variant: "outline" as const,
        },
      },
    };
  }, [mockData]);

  return {
    data: metrics,
    isLoading: false,
    error: null,
  };
}
