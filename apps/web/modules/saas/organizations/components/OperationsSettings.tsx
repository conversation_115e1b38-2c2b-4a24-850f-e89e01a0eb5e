"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import {
  UsersIcon,
  MailIcon,
  BellIcon,
  ShieldIcon,
  CreditCardIcon,
  PaletteIcon,
  SaveIcon,
  DatabaseIcon,
} from "lucide-react";

interface OperationsSettingsProps {
  organizationId: string;
}

export function OperationsSettings({ organizationId }: OperationsSettingsProps) {
  const [settings, setSettings] = useState({
    // Configurações de Clientes
    customers: {
      emailNotifications: true,
      welcomeEmail: true,
      purchaseConfirmation: true,
      marketingEmails: false,
      emailTemplate: "modern",
      newCustomerAlert: true,
      purchaseAlert: true,
      inactiveCustomerAlert: false,
      dataRetention: "2-years",
      allowDataExport: true,
      requireConsent: true,
      defaultView: "table",
      itemsPerPage: "25",
      showAvatars: true,
    },
    // Configurações Financeiras
    finance: {
      autoReconciliation: true,
      dailyReports: true,
      lowBalanceAlert: true,
      transactionNotifications: true,
      reportFormat: "pdf",
      currency: "BRL",
      taxCalculation: true,
      invoiceTemplate: "standard",
      paymentReminders: true,
      overdueNotifications: true,
    },
    // Configurações de Produtos
    products: {
      autoPublish: false,
      stockAlerts: true,
      priceChangeNotifications: true,
      categoryManagement: true,
      bulkOperations: true,
      imageOptimization: true,
      seoOptimization: true,
    },
  });

  const handleSave = () => {
    console.log("Salvando configurações operacionais:", settings);
    // Aqui você implementaria a lógica para salvar as configurações
  };

  return (
    <div className="space-y-6">
      {/* Configurações de Clientes */}
      <SettingsItem
        title="Configurações de Clientes"
        description="Configure como gerenciar e interagir com seus clientes"
      >
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MailIcon className="h-5 w-5" />
                Email e Comunicação
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email de Boas-vindas</Label>
                  <p className="text-xs text-muted-foreground">
                    Enviar automaticamente para novos clientes
                  </p>
                </div>
                <Switch
                  checked={settings.customers.welcomeEmail}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      customers: { ...prev.customers, welcomeEmail: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Confirmação de Compra</Label>
                  <p className="text-xs text-muted-foreground">
                    Enviar recibo após cada compra
                  </p>
                </div>
                <Switch
                  checked={settings.customers.purchaseConfirmation}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      customers: { ...prev.customers, purchaseConfirmation: checked }
                    }))
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>Template de Email</Label>
                <Select
                  value={settings.customers.emailTemplate}
                  onValueChange={(value) =>
                    setSettings(prev => ({
                      ...prev,
                      customers: { ...prev.customers, emailTemplate: value }
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="modern">Moderno</SelectItem>
                    <SelectItem value="classic">Clássico</SelectItem>
                    <SelectItem value="minimal">Minimalista</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BellIcon className="h-5 w-5" />
                Notificações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Novo Cliente</Label>
                  <p className="text-xs text-muted-foreground">
                    Alerta quando um novo cliente se cadastra
                  </p>
                </div>
                <Switch
                  checked={settings.customers.newCustomerAlert}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      customers: { ...prev.customers, newCustomerAlert: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Nova Compra</Label>
                  <p className="text-xs text-muted-foreground">
                    Alerta quando um cliente faz uma compra
                  </p>
                </div>
                <Switch
                  checked={settings.customers.purchaseAlert}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      customers: { ...prev.customers, purchaseAlert: checked }
                    }))
                  }
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </SettingsItem>

      {/* Configurações Financeiras */}
      <SettingsItem
        title="Configurações Financeiras"
        description="Configure relatórios, notificações e processamento financeiro"
      >
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCardIcon className="h-5 w-5" />
                Processamento
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Reconciliação Automática</Label>
                  <p className="text-xs text-muted-foreground">
                    Reconciliar transações automaticamente
                  </p>
                </div>
                <Switch
                  checked={settings.finance.autoReconciliation}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      finance: { ...prev.finance, autoReconciliation: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Cálculo de Impostos</Label>
                  <p className="text-xs text-muted-foreground">
                    Calcular impostos automaticamente
                  </p>
                </div>
                <Switch
                  checked={settings.finance.taxCalculation}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      finance: { ...prev.finance, taxCalculation: checked }
                    }))
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>Moeda Padrão</Label>
                <Select
                  value={settings.finance.currency}
                  onValueChange={(value) =>
                    setSettings(prev => ({
                      ...prev,
                      finance: { ...prev.finance, currency: value }
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BRL">Real (BRL)</SelectItem>
                    <SelectItem value="USD">Dólar (USD)</SelectItem>
                    <SelectItem value="EUR">Euro (EUR)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DatabaseIcon className="h-5 w-5" />
                Relatórios
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Relatórios Diários</Label>
                  <p className="text-xs text-muted-foreground">
                    Gerar relatórios financeiros diários
                  </p>
                </div>
                <Switch
                  checked={settings.finance.dailyReports}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      finance: { ...prev.finance, dailyReports: checked }
                    }))
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>Formato de Relatório</Label>
                <Select
                  value={settings.finance.reportFormat}
                  onValueChange={(value) =>
                    setSettings(prev => ({
                      ...prev,
                      finance: { ...prev.finance, reportFormat: value }
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="excel">Excel</SelectItem>
                    <SelectItem value="csv">CSV</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>
      </SettingsItem>

      {/* Configurações de Produtos */}
      <SettingsItem
        title="Configurações de Produtos"
        description="Configure como gerenciar seus produtos e catálogo"
      >
        <Card>
          <CardContent className="pt-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Publicação Automática</Label>
                  <p className="text-xs text-muted-foreground">
                    Publicar produtos automaticamente após criação
                  </p>
                </div>
                <Switch
                  checked={settings.products.autoPublish}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      products: { ...prev.products, autoPublish: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Alertas de Estoque</Label>
                  <p className="text-xs text-muted-foreground">
                    Notificar quando estoque estiver baixo
                  </p>
                </div>
                <Switch
                  checked={settings.products.stockAlerts}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      products: { ...prev.products, stockAlerts: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Otimização de Imagens</Label>
                  <p className="text-xs text-muted-foreground">
                    Otimizar imagens automaticamente
                  </p>
                </div>
                <Switch
                  checked={settings.products.imageOptimization}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      products: { ...prev.products, imageOptimization: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Otimização SEO</Label>
                  <p className="text-xs text-muted-foreground">
                    Gerar meta tags e URLs otimizadas
                  </p>
                </div>
                <Switch
                  checked={settings.products.seoOptimization}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({
                      ...prev,
                      products: { ...prev.products, seoOptimization: checked }
                    }))
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </SettingsItem>

      {/* Botão de Salvar */}
      <div className="flex justify-end">
        <Button onClick={handleSave} className="min-w-32">
          <SaveIcon className="h-4 w-4 mr-2" />
          Salvar Configurações
        </Button>
      </div>
    </div>
  );
}
