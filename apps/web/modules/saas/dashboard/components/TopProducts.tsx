"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import {
  Package,
  TrendingUp,
  Eye,
  ExternalLink,
  Star,
  ShoppingCart,
  DollarSign,
} from "lucide-react";

interface Product {
  id: string;
  name: string;
  description?: string;
  image?: string;
  price: number;
  sales: number;
  revenue: number;
  rating?: number;
  status: "published" | "draft" | "archived";
  category?: string;
  change: number;
}

interface TopProductsProps {
  products?: Product[];
  isLoading?: boolean;
  maxItems?: number;
}

const getStatusColor = (status: Product["status"]) => {
  switch (status) {
    case "published":
      return "bg-green-100 text-green-800 border-green-200";
    case "draft":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "archived":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getStatusText = (status: Product["status"]) => {
  switch (status) {
    case "published":
      return "Publicado";
    case "draft":
      return "Rascunho";
    case "archived":
      return "Arquivado";
    default:
      return "Desconhecido";
  }
};

const formatCurrency = (cents: number) => {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(cents / 100);
};

const formatNumber = (num: number) => {
  return new Intl.NumberFormat("pt-BR").format(num);
};

export function TopProducts({
  products = [],
  isLoading = false,
  maxItems = 5
}: TopProductsProps) {
  const displayProducts = products.slice(0, maxItems);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Produtos Mais Vendidos
          </CardTitle>
          <CardDescription>
            Seus produtos com melhor performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center gap-3">
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Produtos Mais Vendidos
          </CardTitle>
          <CardDescription>
            Seus produtos com melhor performance
          </CardDescription>
        </div>
        <Button variant="outline" size="sm">
          <Eye className="h-4 w-4 mr-2" />
          Ver todos
        </Button>
      </CardHeader>
      <CardContent>
        {displayProducts.length > 0 ? (
          <div className="space-y-4">
            {displayProducts.map((product, index) => (
              <div key={product.id} className="flex items-center gap-3 p-3 rounded-lg border bg-muted/30 hover:bg-muted/50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-bold text-primary">
                      {index + 1}
                    </div>
                    <Avatar className="h-12 w-12">
                      <AvatarFallback className="bg-muted">
                        {product.image ? (
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <Package className="h-6 w-6 text-muted-foreground" />
                        )}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium text-sm truncate">{product.name}</p>
                        <Badge
                          variant="outline"
                          className={`text-xs ${getStatusColor(product.status)}`}
                        >
                          {getStatusText(product.status)}
                        </Badge>
                      </div>

                      {product.description && (
                        <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                          {product.description}
                        </p>
                      )}

                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <ShoppingCart className="h-3 w-3" />
                          <span>{formatNumber(product.sales)} vendas</span>
                        </div>
                        {product.rating && (
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            <span>{product.rating.toFixed(1)}</span>
                          </div>
                        )}
                        {product.category && (
                          <span className="text-xs bg-muted px-2 py-1 rounded">
                            {product.category}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col items-end gap-1 ml-4">
                      <span className="font-semibold text-sm">
                        {formatCurrency(product.revenue)}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatCurrency(product.price)} cada
                      </span>
                      <div className="flex items-center gap-1 text-xs">
                        <TrendingUp className={`h-3 w-3 ${product.change > 0 ? 'text-green-600' : 'text-red-600'}`} />
                        <span className={product.change > 0 ? 'text-green-600' : 'text-red-600'}>
                          {product.change > 0 ? '+' : ''}{product.change.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
              <Package className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="text-sm text-muted-foreground">
              Nenhum produto encontrado
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Crie seus primeiros produtos para ver as estatísticas aqui
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
