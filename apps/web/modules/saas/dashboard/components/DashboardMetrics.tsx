"use client";

import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import { useMetrics } from "@saas/shared/hooks/useMetrics";
import {
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Target,
  BarChart3,
} from "lucide-react";

interface DashboardMetricsProps {
  organizationId: string;
  isLoading?: boolean;
}

export function DashboardMetrics({ organizationId, isLoading }: DashboardMetricsProps) {
  const { data: metrics, isLoading: metricsLoading } = useMetrics(organizationId);

  const metricConfigs = [
    {
      key: 'revenue' as const,
      icon: DollarSign,
    },
    {
      key: 'sales' as const,
      icon: ShoppingCart,
    },
    {
      key: 'customers' as const,
      icon: Users,
    },
    {
      key: 'products' as const,
      icon: Package,
    },
    {
      key: 'conversion' as const,
      icon: Target,
    },
    {
      key: 'performance' as const,
      icon: BarChart3,
    },
  ];

  return (
    <MetricGrid columns={3}>
      {metricConfigs.map(({ key, icon }) => {
        const metric = metrics?.[key];
        if (!metric) return null;

        return (
          <MetricCard
            key={key}
            title={metric.title}
            value={metric.value}
            change={metric.change}
            isPositive={metric.isPositive}
            icon={icon}
            isLoading={isLoading || metricsLoading}
            description={metric.description}
            badge={metric.badge}
          />
        );
      })}
    </MetricGrid>
  );
}
