"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { Skeleton } from "@ui/components/skeleton";
import { useDashboard } from "../hooks/useDashboard";
import { DashboardMetrics } from "./DashboardMetrics";
import { RecentActivity } from "./RecentActivity";
import { TopProducts } from "./TopProducts";
import {
  RevenueChart,
  TransactionsChart,
  PaymentMethodsChart,
} from "@saas/analytics/components/AnalyticsCharts";
import {
  BarChart3,
  TrendingUp,
  Users,
  CreditCard,
  Download,
  RefreshCw,
  Settings,
  Bell,
  Zap,
  Target,
  Activity,
} from "lucide-react";

interface DashboardClientProps {
  organization: {
    id: string;
    name: string;
    slug: string;
  };
}

export function DashboardClient({ organization }: DashboardClientProps) {
  const { data: dashboard, isLoading, error, refetch } = useDashboard(organization.id);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refetch();
    setIsRefreshing(false);
  };

  const handleExport = () => {
    // Implementar exportação
    console.log("Exporting dashboard data...");
  };

  if (error) {
    return (
      <div className="space-y-6 p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-destructive/10 flex items-center justify-center">
            <Activity className="h-8 w-8 text-destructive" />
          </div>
          <h2 className="text-2xl font-bold text-destructive mb-2">
            Erro ao carregar dashboard
          </h2>
          <p className="text-muted-foreground mb-4">
            Não foi possível carregar os dados do dashboard. Tente novamente.
          </p>
          <Button onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Tentar novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Dashboard
          </h1>
          <p className="text-muted-foreground">
            Visão geral completa da sua plataforma
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configurações
          </Button>
        </div>
      </div>



      {/* Main Metrics */}
      <DashboardMetrics organizationId={organization.id} isLoading={isLoading} />

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="sales">Vendas</TabsTrigger>

        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {isLoading ? (
              <>
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[400px] w-full" />
              </>
            ) : (
              <>
                <RevenueChart data={dashboard?.charts.revenueChart || []} />
                <TransactionsChart data={dashboard?.charts.revenueChart || []} />
              </>
            )}
          </div>


        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {isLoading ? (
              <>
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[400px] w-full" />
              </>
            ) : (
              <>
                <RevenueChart data={dashboard?.charts.revenueChart || []} />
                <PaymentMethodsChart data={[]} />
              </>
            )}
          </div>


        </TabsContent>


      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesse rapidamente as funcionalidades mais importantes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <BarChart3 className="h-6 w-6" />
              <span className="text-sm">Analytics</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <CreditCard className="h-6 w-6" />
              <span className="text-sm">Pagamentos</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <Users className="h-6 w-6" />
              <span className="text-sm">Clientes</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <Settings className="h-6 w-6" />
              <span className="text-sm">Configurações</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
