import { useQuery } from "@tanstack/react-query";

export interface DashboardMetrics {
  revenue: {
    total: number;
    change: number;
    monthly: number;
    monthlyChange: number;
  };
  sales: {
    total: number;
    change: number;
    today: number;
    todayChange: number;
  };
  customers: {
    total: number;
    change: number;
    active: number;
    activeChange: number;
  };
  products: {
    total: number;
    change: number;
    published: number;
    publishedChange: number;
  };
  conversion: {
    rate: number;
    change: number;
  };
  performance: {
    score: number;
    change: number;
  };
}

export interface ActivityItem {
  id: string;
  type: "sale" | "customer" | "product" | "payment";
  title: string;
  description: string;
  amount?: number;
  status: "completed" | "pending" | "failed";
  timestamp: string;
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  metadata?: Record<string, any>;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  image?: string;
  price: number;
  sales: number;
  revenue: number;
  rating?: number;
  status: "published" | "draft" | "archived";
  category?: string;
  change: number;
}

export interface DashboardData {
  metrics: DashboardMetrics;
  activities: ActivityItem[];
  topProducts: Product[];
  charts: {
    revenueChart: Array<{
      month: string;
      revenue: number;
      sales: number;
    }>;
    salesChart: Array<{
      day: string;
      sales: number;
      revenue: number;
    }>;
  };
}

export function useDashboard(organizationId: string) {
  return useQuery({
    queryKey: ["dashboard", organizationId],
    queryFn: async (): Promise<DashboardData> => {
      const response = await fetch(`/api/dashboard/organization/${organizationId}`, {
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch dashboard data");
      }

      return response.json();
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
}
