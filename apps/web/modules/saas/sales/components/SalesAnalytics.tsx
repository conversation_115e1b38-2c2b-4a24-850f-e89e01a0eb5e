"use client";

import { useState, useEffect } from "react";
import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import {
  DollarSignIcon,
  TrendingUpIcon,
  ShoppingCartIcon,
  CalendarIcon,
  UsersIcon,
  ArrowDownIcon
} from "lucide-react";

interface SalesAnalyticsProps {
  organizationId: string;
  productIds?: string[];
}

// Mock data - in real implementation, fetch from API
const mockSalesMetrics = {
  totalOrders: 1247,
  totalRevenue: 8975000, // in cents
  todayRevenue: 289000, // in cents
  averageOrderValue: 720000, // in cents
  conversionRate: 3.2,
  refundRate: 1.8,
  monthlyGrowth: 7.9,
  todayGrowth: -7.4,
  conversionGrowth: 14.3,
  refundGrowth: -21.7, // Lower refund rate is good
};

export function SalesAnalytics({ organizationId, productIds }: SalesAnalyticsProps) {
  const [isLoading, setIsLoading] = useState(false);

  // In real implementation, fetch metrics based on organizationId and productIds
  useEffect(() => {
    // Simulate API call
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [organizationId, productIds]);

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(cents / 100);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatChange = (change: number) => {
    return `${change > 0 ? '+' : ''}${change.toFixed(1)}%`;
  };

  const salesMetrics = [
    {
      title: "Total de Pedidos",
      value: mockSalesMetrics.totalOrders.toLocaleString('pt-BR'),
      change: formatChange(mockSalesMetrics.monthlyGrowth),
      isPositive: mockSalesMetrics.monthlyGrowth > 0,
      icon: ShoppingCartIcon,
      description: "Pedidos realizados",
      badge: {
        text: "Total",
        variant: "default" as const,
      },
    },
    {
      title: "Receita de Hoje",
      value: formatCurrency(mockSalesMetrics.todayRevenue),
      change: formatChange(mockSalesMetrics.todayGrowth),
      isPositive: mockSalesMetrics.todayGrowth > 0,
      icon: CalendarIcon,
      description: "Receita do dia atual",
      badge: {
        text: "Hoje",
        variant: "secondary" as const,
      },
    },
    {
      title: "Receita Total",
      value: formatCurrency(mockSalesMetrics.totalRevenue),
      change: formatChange(mockSalesMetrics.monthlyGrowth),
      isPositive: mockSalesMetrics.monthlyGrowth > 0,
      icon: DollarSignIcon,
      description: "Receita acumulada",
      badge: {
        text: "Total",
        variant: "outline" as const,
      },
      variant: "gradient" as const,
    },
    {
      title: "Ticket Médio",
      value: formatCurrency(mockSalesMetrics.averageOrderValue),
      change: "+1.1%",
      isPositive: true,
      icon: TrendingUpIcon,
      description: "Valor médio por pedido",
      badge: {
        text: "Médio",
        variant: "secondary" as const,
      },
    },
    {
      title: "Taxa de Conversão",
      value: formatPercentage(mockSalesMetrics.conversionRate),
      change: formatChange(mockSalesMetrics.conversionGrowth),
      isPositive: mockSalesMetrics.conversionGrowth > 0,
      icon: UsersIcon,
      description: "Visitantes que compram",
      badge: {
        text: "Performance",
        variant: "default" as const,
      },
    },
    {
      title: "Taxa de Reembolso",
      value: formatPercentage(mockSalesMetrics.refundRate),
      change: formatChange(mockSalesMetrics.refundGrowth),
      isPositive: mockSalesMetrics.refundGrowth < 0, // Lower refund rate is good
      icon: ArrowDownIcon,
      description: "Taxa de reembolsos",
      badge: {
        text: "Baixa",
        variant: "outline" as const,
      },
    },
  ];

  return (
    <div className="space-y-6">
      <MetricGrid columns={3}>
        {salesMetrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            change={metric.change}
            isPositive={metric.isPositive}
            icon={metric.icon}
            isLoading={isLoading}
            description={metric.description}
            badge={metric.badge}
            variant={metric.variant}
          />
        ))}
      </MetricGrid>
    </div>
  );
}

