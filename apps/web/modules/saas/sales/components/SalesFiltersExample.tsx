"use client";

import { AdvancedFiltersSheet, FilterConfig } from "@saas/shared/components/AdvancedFiltersSheet";
import { useAdvancedFilters } from "@saas/shared/hooks/useAdvancedFilters";
import {
  DollarSignIcon,
  CalendarIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  CreditCardIcon,
  PackageIcon,
  MapPinIcon,
  UsersIcon
} from "lucide-react";

export function SalesFiltersExample() {
  const { filters, updateFilters, clearFilters } = useAdvancedFilters({
    initialFilters: {
      searchTerm: "",
      status: [],
      paymentMethod: [],
      amountRange: { min: null, max: null },
      dateRange: { min: null, max: null },
      location: "",
      customerType: [],
    },
    onFiltersChange: (newFilters) => {
      console.log("Filtros de vendas aplicados:", newFilters);
    }
  });

  // Configuração dos filtros para vendas
  const salesFilterConfigs: FilterConfig[] = [
    {
      type: 'checkbox',
      key: 'status',
      label: 'Status da Venda',
      icon: CheckCircleIcon,
      options: [
        { id: 'completed', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: CheckCircleIcon, color: 'text-green-600', count: 1247 },
        { id: 'pending', name: 'Pendente', icon: ClockIcon, color: 'text-yellow-600', count: 89 },
        { id: 'cancelled', name: 'Cancelada', icon: XCircleIcon, color: 'text-red-600', count: 23 },
        { id: 'refunded', name: 'Reembolsada', icon: XCircleIcon, color: 'text-gray-600', count: 12 },
      ]
    },
    {
      type: 'checkbox',
      key: 'paymentMethod',
      label: 'Método de Pagamento',
      icon: CreditCardIcon,
      options: [
        { id: 'pix', name: 'PIX', icon: CreditCardIcon, color: 'text-green-600', count: 856 },
        { id: 'credit_card', name: 'Cartão de Crédito', icon: CreditCardIcon, color: 'text-blue-600', count: 432 },
        { id: 'debit_card', name: 'Cartão de Débito', icon: CreditCardIcon, color: 'text-purple-600', count: 234 },
        { id: 'bank_transfer', name: 'Transferência', icon: CreditCardIcon, color: 'text-orange-600', count: 89 },
      ]
    },
    {
      type: 'range',
      key: 'amountRange',
      label: 'Valor da Venda',
      icon: DollarSignIcon,
      min: 0,
      max: 10000,
      step: 50
    },
    {
      type: 'range',
      key: 'dateRange',
      label: 'Data da Venda',
      icon: CalendarIcon,
      min: 2020,
      max: 2024,
      step: 1
    },
    {
      type: 'input',
      key: 'location',
      label: 'Localização',
      icon: MapPinIcon,
      placeholder: 'Cidade ou estado do cliente'
    },
    {
      type: 'checkbox',
      key: 'customerType',
      label: 'Tipo de Cliente',
      icon: UsersIcon,
      options: [
        { id: 'new', name: 'Novo Cliente', icon: UsersIcon, color: 'text-blue-600', count: 456 },
        { id: 'returning', name: 'Cliente Recorrente', icon: UsersIcon, color: 'text-green-600', count: 789 },
        { id: 'vip', name: 'Cliente VIP', icon: UsersIcon, color: 'text-purple-600', count: 123 },
      ]
    }
  ];

  return (
    <AdvancedFiltersSheet
      onFiltersChange={updateFilters}
      activeFilters={filters}
      filterConfigs={salesFilterConfigs}
      totalItems={1363}
      title="Filtros de Vendas"
      description="Aplique filtros para analisar suas vendas"
      triggerLabel="Filtros"
      searchPlaceholder="Buscar por ID, cliente ou produto..."
      onClearFilters={clearFilters}
    />
  );
}
