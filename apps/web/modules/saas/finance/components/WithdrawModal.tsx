"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Card, CardContent } from "@ui/components/card";
import { ArrowUpRightIcon, WalletIcon, AlertCircleIcon, CheckCircleIcon } from "lucide-react";
import { Badge } from "@ui/components/badge";

interface WithdrawModalProps {
  availableBalance: number;
  children: React.ReactNode;
}

export function WithdrawModal({ availableBalance, children }: WithdrawModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [amount, setAmount] = useState("");
  const [bankAccount, setBankAccount] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const handleWithdraw = async () => {
    setIsProcessing(true);
    // Simular processamento
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsProcessing(false);
    setIsOpen(false);
    // Aqui seria feita a chamada para a API de saque
  };

  const isValidAmount = amount && parseFloat(amount) > 0 && parseFloat(amount) <= availableBalance;
  const withdrawFee = amount ? parseFloat(amount) * 0.02 : 0; // 2% de taxa
  const netAmount = amount ? parseFloat(amount) - withdrawFee : 0;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowUpRightIcon className="h-5 w-5" />
            Solicitar Saque
          </DialogTitle>
          <DialogDescription>
            Transfira seus fundos para sua conta bancária
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Saldo Disponível */}
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-800">Saldo Disponível</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(availableBalance)}
                  </p>
                </div>
                <WalletIcon className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          {/* Formulário de Saque */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Valor do Saque</Label>
              <Input
                id="amount"
                type="number"
                placeholder="0,00"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="text-lg"
              />
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAmount((availableBalance * 0.25).toString())}
                >
                  25%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAmount((availableBalance * 0.5).toString())}
                >
                  50%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAmount(availableBalance.toString())}
                >
                  Total
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bankAccount">Conta Bancária</Label>
              <Select value={bankAccount} onValueChange={setBankAccount}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma conta" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="account1">
                    Banco do Brasil - ****1234
                  </SelectItem>
                  <SelectItem value="account2">
                    Itaú - ****5678
                  </SelectItem>
                  <SelectItem value="account3">
                    Bradesco - ****9012
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Resumo do Saque */}
            {amount && (
              <Card className="bg-muted/50">
                <CardContent className="p-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Valor solicitado:</span>
                    <span className="font-medium">{formatCurrency(parseFloat(amount))}</span>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Taxa de processamento (2%):</span>
                    <span>-{formatCurrency(withdrawFee)}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between text-sm font-semibold">
                      <span>Valor líquido:</span>
                      <span className="text-green-600">{formatCurrency(netAmount)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Validações */}
            {amount && parseFloat(amount) > availableBalance && (
              <div className="flex items-center gap-2 text-sm text-red-600">
                <AlertCircleIcon className="h-4 w-4" />
                <span>Valor excede o saldo disponível</span>
              </div>
            )}

            {amount && parseFloat(amount) < 10 && (
              <div className="flex items-center gap-2 text-sm text-yellow-600">
                <AlertCircleIcon className="h-4 w-4" />
                <span>Valor mínimo para saque é R$ 10,00</span>
              </div>
            )}

            {isValidAmount && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircleIcon className="h-4 w-4" />
                <span>Valor válido para saque</span>
              </div>
            )}
          </div>

          {/* Ações */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleWithdraw}
              disabled={!isValidAmount || !bankAccount || isProcessing}
              className="flex-1"
            >
              {isProcessing ? "Processando..." : "Confirmar Saque"}
            </Button>
          </div>

          {/* Informações Adicionais */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p>• O saque será processado em até 2 dias úteis</p>
            <p>• Taxa de processamento de 2% será descontada</p>
            <p>• Você receberá um e-mail de confirmação</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
