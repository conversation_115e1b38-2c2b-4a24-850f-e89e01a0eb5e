"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import {
  Grid3X3Icon,
  ListIcon,
  SearchIcon,
  FilterIcon,
  PlusIcon,
  DownloadIcon,
  RefreshCwIcon,
  CheckSquareIcon,
  SquareIcon,
} from "lucide-react";
import { EnhancedProductCard, EnhancedProduct } from "./EnhancedProductCard";
import { MobileProductCard } from "./MobileProductCard";
import { ProductCardSkeletonGrid } from "./ProductCardSkeleton";
import { useMediaQuery } from "@ui/hooks/use-media-query";

interface EnhancedProductsGridProps {
  products: EnhancedProduct[];
  organizationSlug: string;
  isLoading?: boolean;
  onQuickAction?: (productId: string, action: string) => void;
  onBulkAction?: (productIds: string[], action: string) => void;
  onCreateProduct?: () => void;
  onRefresh?: () => void;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  statusFilter?: string;
  onStatusFilterChange?: (value: string) => void;
  typeFilter?: string;
  onTypeFilterChange?: (value: string) => void;
}

export function EnhancedProductsGrid({
  products,
  organizationSlug,
  isLoading = false,
  onQuickAction,
  onBulkAction,
  onCreateProduct,
  onRefresh,
  searchValue = "",
  onSearchChange,
  statusFilter = "all",
  onStatusFilterChange,
  typeFilter = "all",
  onTypeFilterChange,
}: EnhancedProductsGridProps) {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Auto-switch to list view on mobile
  useEffect(() => {
    if (isMobile && viewMode === "grid") {
      setViewMode("list");
    }
  }, [isMobile, viewMode]);

  const handleSelectAll = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map(p => p.id));
    }
  };

  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleBulkAction = async (action: string) => {
    if (selectedProducts.length === 0) return;
    await onBulkAction?.(selectedProducts, action);
    setSelectedProducts([]);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh?.();
    } finally {
      setIsRefreshing(false);
    }
  };

  // Calculate stats
  const stats = {
    total: products.length,
    published: products.filter(p => p.status === "PUBLISHED").length,
    draft: products.filter(p => p.status === "DRAFT").length,
    totalRevenue: products.reduce((sum, p) => sum + (p.salesMetrics?.totalRevenue || 0), 0),
    totalSales: products.reduce((sum, p) => sum + (p.salesMetrics?.totalSales || 0), 0),
  };

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(cents / 100);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-48 bg-muted rounded animate-pulse" />
            <div className="h-4 w-64 bg-muted rounded animate-pulse" />
          </div>
          <div className="h-10 w-32 bg-muted rounded animate-pulse" />
        </div>
        
        {/* Stats Skeleton */}
        <div className="grid gap-4 md:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="h-24 bg-muted rounded-lg animate-pulse" />
          ))}
        </div>
        
        {/* Products Skeleton */}
        <ProductCardSkeletonGrid count={6} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Produtos</h1>
          <p className="text-muted-foreground">
            Gerencie seus produtos e acompanhe o desempenho das vendas
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCwIcon className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
            Atualizar
          </Button>
          <Button onClick={onCreateProduct} size="sm">
            <PlusIcon className="h-4 w-4 mr-2" />
            Novo Produto
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="bg-card border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total de Produtos</p>
              <p className="text-2xl font-bold">{stats.total}</p>
            </div>
            <div className="text-primary">📦</div>
          </div>
        </div>
        <div className="bg-card border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Produtos Ativos</p>
              <p className="text-2xl font-bold text-green-600">{stats.published}</p>
            </div>
            <div className="text-green-600">✅</div>
          </div>
        </div>
        <div className="bg-card border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total de Vendas</p>
              <p className="text-2xl font-bold">{stats.totalSales}</p>
            </div>
            <div className="text-blue-600">🛒</div>
          </div>
        </div>
        <div className="bg-card border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Receita Total</p>
              <p className="text-2xl font-bold text-primary">{formatCurrency(stats.totalRevenue)}</p>
            </div>
            <div className="text-primary">💰</div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar produtos..."
            value={searchValue}
            onChange={(e) => onSearchChange?.(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filters */}
        <div className="flex items-center gap-2">
          <Select value={statusFilter} onValueChange={onStatusFilterChange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="PUBLISHED">Ativos</SelectItem>
              <SelectItem value="DRAFT">Rascunho</SelectItem>
              <SelectItem value="ARCHIVED">Arquivados</SelectItem>
            </SelectContent>
          </Select>

          <Select value={typeFilter} onValueChange={onTypeFilterChange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Tipo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="COURSE">Curso</SelectItem>
              <SelectItem value="EBOOK">E-book</SelectItem>
              <SelectItem value="MENTORSHIP">Mentoria</SelectItem>
              <SelectItem value="SUBSCRIPTION">Assinatura</SelectItem>
            </SelectContent>
          </Select>

          {/* View Mode Toggle */}
          {!isMobile && (
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="h-8 w-8 p-0"
              >
                <Grid3X3Icon className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-8 w-8 p-0"
              >
                <ListIcon className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-primary/5 border border-primary/20 rounded-lg">
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {selectedProducts.length} produto(s) selecionado(s)
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkAction("generate-links")}
            >
              Gerar Links
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkAction("export")}
            >
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkAction("archive")}
            >
              Arquivar
            </Button>
          </div>
        </div>
      )}

      {/* Select All */}
      {products.length > 0 && (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSelectAll}
            className="h-8 px-2"
          >
            {selectedProducts.length === products.length ? (
              <CheckSquareIcon className="h-4 w-4 mr-2" />
            ) : (
              <SquareIcon className="h-4 w-4 mr-2" />
            )}
            {selectedProducts.length === products.length ? "Desmarcar todos" : "Selecionar todos"}
          </Button>
        </div>
      )}

      {/* Products Grid/List */}
      {products.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📦</div>
          <h3 className="text-lg font-semibold mb-2">Nenhum produto encontrado</h3>
          <p className="text-muted-foreground mb-4">
            Comece criando seu primeiro produto para começar a vender
          </p>
          <Button onClick={onCreateProduct}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Criar Primeiro Produto
          </Button>
        </div>
      ) : (
        <div className={cn(
          viewMode === "grid" && !isMobile
            ? "grid gap-6 md:grid-cols-2 lg:grid-cols-3"
            : "space-y-4"
        )}>
          {products.map((product) => (
            viewMode === "grid" && !isMobile ? (
              <EnhancedProductCard
                key={product.id}
                product={product}
                organizationSlug={organizationSlug}
                onQuickAction={onQuickAction}
                isSelected={selectedProducts.includes(product.id)}
                onSelect={() => handleSelectProduct(product.id)}
              />
            ) : (
              <MobileProductCard
                key={product.id}
                product={product}
                organizationSlug={organizationSlug}
                onQuickAction={onQuickAction}
                isSelected={selectedProducts.includes(product.id)}
                onSelect={() => handleSelectProduct(product.id)}
              />
            )
          ))}
        </div>
      )}
    </div>
  );
}
