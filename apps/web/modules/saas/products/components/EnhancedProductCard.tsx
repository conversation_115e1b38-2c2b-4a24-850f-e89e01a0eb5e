"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  MoreHorizontalIcon,
  SettingsIcon,
  LinkIcon,
  BarChart3Icon,
  CopyIcon,
  ArchiveIcon,
  EditIcon,
  EyeIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  XCircleIcon,
  PackageIcon,
  CreditCardIcon,
  ShoppingCartIcon,
  DollarSignIcon,
  UsersIcon,
  CalendarIcon,
} from "lucide-react";
import Link from "next/link";
import { Product } from "@saas/products/hooks/useProducts";

// Enhanced Product interface with payment gateway context
export interface EnhancedProduct extends Product {
  salesMetrics?: {
    totalSales: number;
    totalRevenue: number;
    conversionRate: number;
    checkoutViews: number;
    lastSaleDate?: string;
  };
  gatewayStatus?: {
    isConfigured: boolean;
    activeGateways: string[];
    lastCheckoutTest?: string;
  };
  checkoutMetrics?: {
    abandonment: number;
    averageOrderValue: number;
    topPaymentMethod: string;
  };
}

interface EnhancedProductCardProps {
  product: EnhancedProduct;
  organizationSlug: string;
  onQuickAction?: (productId: string, action: string) => void;
  isSelected?: boolean;
  onSelect?: () => void;
}

export function EnhancedProductCard({
  product,
  organizationSlug,
  onQuickAction,
  isSelected = false,
  onSelect,
}: EnhancedProductCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Format currency
  const formatCurrency = (cents: number, currency = "BRL") => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency,
    }).format(cents / 100);
  };

  // Format date
  const formatDate = (date: string) => {
    return new Intl.DateTimeFormat("pt-BR", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }).format(new Date(date));
  };

  // Get status configuration
  const getStatusConfig = (status: Product["status"]) => {
    const configs = {
      PUBLISHED: {
        label: "Ativo",
        variant: "default" as const,
        icon: CheckCircleIcon,
        className: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400",
      },
      DRAFT: {
        label: "Rascunho",
        variant: "secondary" as const,
        icon: AlertCircleIcon,
        className: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400",
      },
      ARCHIVED: {
        label: "Arquivado",
        variant: "outline" as const,
        icon: XCircleIcon,
        className: "bg-gray-100 text-gray-600 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400",
      },
      SUSPENDED: {
        label: "Suspenso",
        variant: "destructive" as const,
        icon: XCircleIcon,
        className: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400",
      },
    };
    return configs[status] || configs.DRAFT;
  };

  // Get gateway status
  const getGatewayStatusConfig = () => {
    const isConfigured = product.gatewayStatus?.isConfigured ?? false;
    return {
      configured: {
        label: "Gateway Ativo",
        className: "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400",
        icon: CheckCircleIcon,
      },
      notConfigured: {
        label: "Gateway Pendente",
        className: "bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400",
        icon: AlertCircleIcon,
      },
    }[isConfigured ? "configured" : "notConfigured"];
  };

  const statusConfig = getStatusConfig(product.status);
  const gatewayConfig = getGatewayStatusConfig();
  const StatusIcon = statusConfig.icon;
  const GatewayIcon = gatewayConfig.icon;

  const handleQuickAction = async (action: string) => {
    setIsLoading(true);
    try {
      await onQuickAction?.(product.id, action);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card
      className={cn(
        "group relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1",
        "border-border/50 hover:border-primary/30 hover:shadow-primary/10",
        isSelected && "ring-2 ring-primary ring-offset-2",
        product.status === "PUBLISHED" && "bg-gradient-to-br from-green-50/30 to-transparent dark:from-green-950/10",
        product.status === "DRAFT" && "bg-gradient-to-br from-yellow-50/30 to-transparent dark:from-yellow-950/10"
      )}
    >
      {/* Selection Checkbox */}
      {onSelect && (
        <div className="absolute top-3 left-3 z-10">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            className="w-4 h-4 text-primary bg-background border-2 border-border rounded focus:ring-primary focus:ring-2"
          />
        </div>
      )}

      {/* Quick Actions Menu */}
      <div className="absolute top-3 right-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm hover:bg-background"
            >
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem asChild>
              <Link href={`/app/${organizationSlug}/products/${product.id}`}>
                <SettingsIcon className="h-4 w-4 mr-2" />
                Configurar Produto
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleQuickAction("checkout-link")}>
              <LinkIcon className="h-4 w-4 mr-2" />
              Gerar Link de Checkout
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleQuickAction("analytics")}>
              <BarChart3Icon className="h-4 w-4 mr-2" />
              Ver Analytics
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleQuickAction("duplicate")}>
              <CopyIcon className="h-4 w-4 mr-2" />
              Duplicar
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleQuickAction("edit")}>
              <EditIcon className="h-4 w-4 mr-2" />
              Editar
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => handleQuickAction("archive")}
              className="text-destructive focus:text-destructive"
            >
              <ArchiveIcon className="h-4 w-4 mr-2" />
              Arquivar
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <CardHeader className="pb-4">
        {/* Product Image */}
        <div className="aspect-square bg-muted/30 rounded-xl mb-4 flex items-center justify-center overflow-hidden relative group-hover:shadow-lg transition-shadow">
          {product.thumbnail ? (
            <img
              src={product.thumbnail}
              alt={product.name}
              className="w-full h-full object-cover rounded-xl group-hover:scale-105 transition-transform duration-500"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center">
              <PackageIcon className="h-16 w-16 text-primary/40" />
            </div>
          )}
          
          {/* Status Overlay */}
          <div className="absolute top-2 left-2">
            <Badge className={cn("text-xs font-medium border", statusConfig.className)}>
              <StatusIcon className="h-3 w-3 mr-1" />
              {statusConfig.label}
            </Badge>
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-3">
          <div>
            <h3 className="font-bold text-lg leading-tight group-hover:text-primary transition-colors line-clamp-2">
              {product.name}
            </h3>
            {product.shortDescription && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {product.shortDescription}
              </p>
            )}
          </div>

          {/* Price and Type */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-primary">
                  {formatCurrency(product.priceCents, product.currency)}
                </span>
                {product.comparePriceCents && (
                  <span className="text-sm text-muted-foreground line-through">
                    {formatCurrency(product.comparePriceCents, product.currency)}
                  </span>
                )}
              </div>
              <Badge variant="outline" className="text-xs">
                {product.type}
              </Badge>
            </div>
          </div>

          {/* Gateway Status */}
          <div className="flex items-center gap-2">
            <Badge className={cn("text-xs font-medium border", gatewayConfig.className)}>
              <GatewayIcon className="h-3 w-3 mr-1" />
              {gatewayConfig.label}
            </Badge>
            {product.gatewayStatus?.activeGateways && (
              <span className="text-xs text-muted-foreground">
                {product.gatewayStatus.activeGateways.length} gateway(s)
              </span>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Sales Metrics */}
        {product.salesMetrics && (
          <div className="grid grid-cols-2 gap-3 mb-4 p-3 bg-muted/30 rounded-lg">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 mb-1">
                <ShoppingCartIcon className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Vendas</span>
              </div>
              <p className="font-semibold text-sm">{product.salesMetrics.totalSales}</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 mb-1">
                <DollarSignIcon className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Receita</span>
              </div>
              <p className="font-semibold text-sm">
                {formatCurrency(product.salesMetrics.totalRevenue)}
              </p>
            </div>
          </div>
        )}

        {/* Conversion Rate */}
        {product.salesMetrics?.conversionRate !== undefined && (
          <div className="flex items-center justify-between mb-4 p-2 bg-primary/5 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                {product.salesMetrics.conversionRate > 5 ? (
                  <TrendingUpIcon className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDownIcon className="h-4 w-4 text-orange-600" />
                )}
                <span className="text-sm font-medium">
                  {product.salesMetrics.conversionRate.toFixed(1)}%
                </span>
              </div>
              <span className="text-xs text-muted-foreground">conversão</span>
            </div>
            {product.salesMetrics.checkoutViews > 0 && (
              <div className="flex items-center gap-1">
                <EyeIcon className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {product.salesMetrics.checkoutViews} views
                </span>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-2">
          <Button
            asChild
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
            size="sm"
          >
            <Link href={`/app/${organizationSlug}/products/${product.id}`}>
              <SettingsIcon className="h-4 w-4 mr-2" />
              Configurar Produto
            </Link>
          </Button>
          
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickAction("checkout-link")}
              disabled={isLoading}
              className="text-xs"
            >
              <LinkIcon className="h-3 w-3 mr-1" />
              Checkout
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickAction("analytics")}
              disabled={isLoading}
              className="text-xs"
            >
              <BarChart3Icon className="h-3 w-3 mr-1" />
              Analytics
            </Button>
          </div>
        </div>

        {/* Footer Info */}
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-border/50 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <CalendarIcon className="h-3 w-3" />
            <span>Criado {formatDate(product.createdAt)}</span>
          </div>
          {product.salesMetrics?.lastSaleDate && (
            <div className="flex items-center gap-1">
              <span>Última venda {formatDate(product.salesMetrics.lastSaleDate)}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
