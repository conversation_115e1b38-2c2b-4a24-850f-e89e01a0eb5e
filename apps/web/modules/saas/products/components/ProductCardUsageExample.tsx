"use client";

import { useState } from "react";
import { EnhancedProductsGrid } from "./EnhancedProductsGrid";
import { EnhancedProduct } from "./EnhancedProductCard";
import { toast } from "sonner";

// Example usage component showing how to integrate the enhanced product cards
export function ProductCardUsageExample() {
  const [products, setProducts] = useState<EnhancedProduct[]>([
    {
      id: "1",
      organizationId: "org-1",
      creatorId: "user-1",
      name: "Curso Completo de Marketing Digital",
      slug: "curso-marketing-digital",
      description: "Aprenda marketing digital do zero ao avançado com estratégias práticas e atualizadas.",
      shortDescription: "Marketing digital completo para iniciantes e avançados",
      priceCents: 49700, // R$ 497,00
      comparePriceCents: 99700, // R$ 997,00
      currency: "BRL",
      type: "COURSE",
      status: "PUBLISHED",
      visibility: "PUBLIC",
      thumbnail: "/images/products/marketing-course.jpg",
      gallery: [],
      tags: ["marketing", "digital", "vendas"],
      features: ["Acesso vitalício", "Certificado", "Suporte"],
      requirements: ["Computador", "Internet"],
      duration: 2400, // 40 horas
      level: "BEGINNER",
      language: "pt-BR",
      certificate: true,
      downloadable: false,
      checkoutType: "DEFAULT",
      settings: {},
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-20T15:30:00Z",
      
      // Enhanced data for payment gateway context
      salesMetrics: {
        totalSales: 156,
        totalRevenue: 7753200, // R$ 77,532.00
        conversionRate: 8.5,
        checkoutViews: 1835,
        lastSaleDate: "2024-01-20T14:22:00Z",
      },
      gatewayStatus: {
        isConfigured: true,
        activeGateways: ["stripe", "mercadopago", "pix"],
        lastCheckoutTest: "2024-01-19T09:15:00Z",
      },
      checkoutMetrics: {
        abandonment: 15.2,
        averageOrderValue: 49700,
        topPaymentMethod: "credit_card",
      },
    },
    {
      id: "2",
      organizationId: "org-1",
      creatorId: "user-1",
      name: "E-book: Estratégias de Vendas Online",
      slug: "ebook-vendas-online",
      description: "Guia completo com 50 estratégias comprovadas para aumentar suas vendas online.",
      shortDescription: "50 estratégias de vendas online",
      priceCents: 9700, // R$ 97,00
      comparePriceCents: 19700, // R$ 197,00
      currency: "BRL",
      type: "EBOOK",
      status: "PUBLISHED",
      visibility: "PUBLIC",
      thumbnail: "/images/products/sales-ebook.jpg",
      gallery: [],
      tags: ["vendas", "ebook", "estratégias"],
      features: ["PDF de alta qualidade", "Planilhas bônus", "Checklist"],
      requirements: ["Leitor de PDF"],
      duration: null,
      level: "INTERMEDIATE",
      language: "pt-BR",
      certificate: false,
      downloadable: true,
      checkoutType: "DEFAULT",
      settings: {},
      createdAt: "2024-01-10T08:00:00Z",
      updatedAt: "2024-01-18T12:45:00Z",
      
      // Enhanced data
      salesMetrics: {
        totalSales: 89,
        totalRevenue: 863300, // R$ 8,633.00
        conversionRate: 12.3,
        checkoutViews: 724,
        lastSaleDate: "2024-01-19T16:45:00Z",
      },
      gatewayStatus: {
        isConfigured: true,
        activeGateways: ["stripe", "pix"],
        lastCheckoutTest: "2024-01-18T11:30:00Z",
      },
      checkoutMetrics: {
        abandonment: 8.7,
        averageOrderValue: 9700,
        topPaymentMethod: "pix",
      },
    },
    {
      id: "3",
      organizationId: "org-1",
      creatorId: "user-1",
      name: "Mentoria Individual - Growth Hacking",
      slug: "mentoria-growth-hacking",
      description: "Mentoria personalizada para acelerar o crescimento do seu negócio digital.",
      shortDescription: "Mentoria 1:1 para growth hacking",
      priceCents: 199700, // R$ 1,997.00
      currency: "BRL",
      type: "MENTORSHIP",
      status: "DRAFT",
      visibility: "PRIVATE",
      thumbnail: "/images/products/mentorship.jpg",
      gallery: [],
      tags: ["mentoria", "growth", "hacking"],
      features: ["4 sessões de 1h", "Plano personalizado", "Suporte WhatsApp"],
      requirements: ["Negócio ativo", "Disponibilidade"],
      duration: 240, // 4 horas
      level: "ADVANCED",
      language: "pt-BR",
      certificate: false,
      downloadable: false,
      checkoutType: "CUSTOM",
      settings: { requiresApproval: true },
      createdAt: "2024-01-18T14:00:00Z",
      updatedAt: "2024-01-19T10:20:00Z",
      
      // Enhanced data
      salesMetrics: {
        totalSales: 0,
        totalRevenue: 0,
        conversionRate: 0,
        checkoutViews: 0,
      },
      gatewayStatus: {
        isConfigured: false,
        activeGateways: [],
      },
      checkoutMetrics: {
        abandonment: 0,
        averageOrderValue: 0,
        topPaymentMethod: "",
      },
    },
  ]);

  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(false);

  // Filter products based on search and filters
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchValue.toLowerCase()) ||
                         product.description?.toLowerCase().includes(searchValue.toLowerCase());
    const matchesStatus = statusFilter === "all" || product.status === statusFilter;
    const matchesType = typeFilter === "all" || product.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const handleQuickAction = async (productId: string, action: string) => {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    switch (action) {
      case "checkout-link":
        // Simulate generating checkout link
        await new Promise(resolve => setTimeout(resolve, 1000));
        const checkoutUrl = `https://checkout.example.com/${product.slug}`;
        navigator.clipboard.writeText(checkoutUrl);
        toast.success("Link de checkout copiado para a área de transferência!");
        break;
        
      case "analytics":
        // Navigate to analytics page
        toast.info(`Abrindo analytics para ${product.name}`);
        break;
        
      case "duplicate":
        // Duplicate product
        const duplicatedProduct: EnhancedProduct = {
          ...product,
          id: `${product.id}-copy`,
          name: `${product.name} (Cópia)`,
          slug: `${product.slug}-copy`,
          status: "DRAFT",
          salesMetrics: {
            totalSales: 0,
            totalRevenue: 0,
            conversionRate: 0,
            checkoutViews: 0,
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setProducts(prev => [...prev, duplicatedProduct]);
        toast.success("Produto duplicado com sucesso!");
        break;
        
      case "archive":
        // Archive product
        setProducts(prev => 
          prev.map(p => 
            p.id === productId 
              ? { ...p, status: "ARCHIVED" as const }
              : p
          )
        );
        toast.success("Produto arquivado!");
        break;
        
      default:
        toast.info(`Ação "${action}" executada para ${product.name}`);
    }
  };

  const handleBulkAction = async (productIds: string[], action: string) => {
    switch (action) {
      case "generate-links":
        toast.success(`Links gerados para ${productIds.length} produto(s)!`);
        break;
      case "export":
        toast.success(`${productIds.length} produto(s) exportado(s)!`);
        break;
      case "archive":
        setProducts(prev => 
          prev.map(p => 
            productIds.includes(p.id)
              ? { ...p, status: "ARCHIVED" as const }
              : p
          )
        );
        toast.success(`${productIds.length} produto(s) arquivado(s)!`);
        break;
    }
  };

  const handleCreateProduct = () => {
    toast.info("Abrindo formulário de criação de produto...");
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsLoading(false);
    toast.success("Produtos atualizados!");
  };

  return (
    <div className="container mx-auto py-8">
      <EnhancedProductsGrid
        products={filteredProducts}
        organizationSlug="example-org"
        isLoading={isLoading}
        onQuickAction={handleQuickAction}
        onBulkAction={handleBulkAction}
        onCreateProduct={handleCreateProduct}
        onRefresh={handleRefresh}
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        typeFilter={typeFilter}
        onTypeFilterChange={setTypeFilter}
      />
    </div>
  );
}
