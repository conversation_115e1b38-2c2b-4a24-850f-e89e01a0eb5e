"use client";

import { useState } from "react";
import { Card, CardContent } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  MoreVerticalIcon,
  SettingsIcon,
  LinkIcon,
  BarChart3Icon,
  CheckCircleIcon,
  AlertCircleIcon,
  XCircleIcon,
  PackageIcon,
  ShoppingCartIcon,
  DollarSignIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon,
} from "lucide-react";
import Link from "next/link";
import { Product } from "@saas/products/hooks/useProducts";

interface MobileProductCardProps {
  product: Product & {
    salesMetrics?: {
      totalSales: number;
      totalRevenue: number;
      conversionRate: number;
      checkoutViews: number;
    };
    gatewayStatus?: {
      isConfigured: boolean;
      activeGateways: string[];
    };
  };
  organizationSlug: string;
  onQuickAction?: (productId: string, action: string) => void;
  isSelected?: boolean;
  onSelect?: () => void;
}

export function MobileProductCard({
  product,
  organizationSlug,
  onQuickAction,
  isSelected = false,
  onSelect,
}: MobileProductCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Format currency
  const formatCurrency = (cents: number, currency = "BRL") => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency,
    }).format(cents / 100);
  };

  // Get status configuration
  const getStatusConfig = (status: Product["status"]) => {
    const configs = {
      PUBLISHED: {
        label: "Ativo",
        icon: CheckCircleIcon,
        className: "bg-green-100 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400",
      },
      DRAFT: {
        label: "Rascunho",
        icon: AlertCircleIcon,
        className: "bg-yellow-100 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400",
      },
      ARCHIVED: {
        label: "Arquivado",
        icon: XCircleIcon,
        className: "bg-gray-100 text-gray-600 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400",
      },
      SUSPENDED: {
        label: "Suspenso",
        icon: XCircleIcon,
        className: "bg-red-100 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400",
      },
    };
    return configs[status] || configs.DRAFT;
  };

  const statusConfig = getStatusConfig(product.status);
  const StatusIcon = statusConfig.icon;

  const handleQuickAction = async (action: string) => {
    setIsLoading(true);
    try {
      await onQuickAction?.(product.id, action);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card
      className={cn(
        "overflow-hidden transition-all duration-200 active:scale-[0.98]",
        "border-border/50 hover:border-primary/30",
        isSelected && "ring-2 ring-primary ring-offset-1",
        product.status === "PUBLISHED" && "bg-gradient-to-r from-green-50/50 to-transparent dark:from-green-950/10"
      )}
    >
      <CardContent className="p-4">
        <div className="flex gap-3">
          {/* Selection Checkbox */}
          {onSelect && (
            <div className="flex-shrink-0 pt-1">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={onSelect}
                className="w-4 h-4 text-primary bg-background border-2 border-border rounded focus:ring-primary focus:ring-2"
              />
            </div>
          )}

          {/* Product Image */}
          <div className="flex-shrink-0">
            <div className="w-16 h-16 bg-muted/30 rounded-lg flex items-center justify-center overflow-hidden">
              {product.thumbnail ? (
                <img
                  src={product.thumbnail}
                  alt={product.name}
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <PackageIcon className="h-8 w-8 text-primary/40" />
              )}
            </div>
          </div>

          {/* Product Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-base leading-tight truncate">
                  {product.name}
                </h3>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={cn("text-xs font-medium border", statusConfig.className)}>
                    <StatusIcon className="h-3 w-3 mr-1" />
                    {statusConfig.label}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {product.type}
                  </Badge>
                </div>
              </div>

              {/* Quick Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 flex-shrink-0"
                  >
                    <MoreVerticalIcon className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem asChild>
                    <Link href={`/app/${organizationSlug}/products/${product.id}`}>
                      <SettingsIcon className="h-4 w-4 mr-2" />
                      Configurar
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleQuickAction("checkout-link")}>
                    <LinkIcon className="h-4 w-4 mr-2" />
                    Link Checkout
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleQuickAction("analytics")}>
                    <BarChart3Icon className="h-4 w-4 mr-2" />
                    Analytics
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Price */}
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg font-bold text-primary">
                {formatCurrency(product.priceCents, product.currency)}
              </span>
              {product.comparePriceCents && (
                <span className="text-sm text-muted-foreground line-through">
                  {formatCurrency(product.comparePriceCents, product.currency)}
                </span>
              )}
            </div>

            {/* Metrics Row */}
            {product.salesMetrics && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground mb-3">
                <div className="flex items-center gap-1">
                  <ShoppingCartIcon className="h-3 w-3" />
                  <span>{product.salesMetrics.totalSales} vendas</span>
                </div>
                <div className="flex items-center gap-1">
                  <DollarSignIcon className="h-3 w-3" />
                  <span>{formatCurrency(product.salesMetrics.totalRevenue)}</span>
                </div>
                {product.salesMetrics.conversionRate > 0 && (
                  <div className="flex items-center gap-1">
                    {product.salesMetrics.conversionRate > 5 ? (
                      <TrendingUpIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <TrendingDownIcon className="h-3 w-3 text-orange-600" />
                    )}
                    <span>{product.salesMetrics.conversionRate.toFixed(1)}%</span>
                  </div>
                )}
              </div>
            )}

            {/* Gateway Status */}
            {product.gatewayStatus && (
              <div className="flex items-center gap-2 mb-3">
                <Badge
                  className={cn(
                    "text-xs font-medium border",
                    product.gatewayStatus.isConfigured
                      ? "bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400"
                      : "bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400"
                  )}
                >
                  {product.gatewayStatus.isConfigured ? (
                    <CheckCircleIcon className="h-3 w-3 mr-1" />
                  ) : (
                    <AlertCircleIcon className="h-3 w-3 mr-1" />
                  )}
                  {product.gatewayStatus.isConfigured ? "Gateway Ativo" : "Gateway Pendente"}
                </Badge>
                {product.gatewayStatus.activeGateways.length > 0 && (
                  <span className="text-xs text-muted-foreground">
                    {product.gatewayStatus.activeGateways.length} gateway(s)
                  </span>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                asChild
                size="sm"
                className="flex-1 h-8 text-xs"
              >
                <Link href={`/app/${organizationSlug}/products/${product.id}`}>
                  <SettingsIcon className="h-3 w-3 mr-1" />
                  Configurar
                </Link>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction("checkout-link")}
                disabled={isLoading}
                className="h-8 text-xs"
              >
                <LinkIcon className="h-3 w-3 mr-1" />
                Checkout
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
