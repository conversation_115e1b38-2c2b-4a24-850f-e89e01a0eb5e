"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
export interface Pixel {
  id: string;
  organizationId: string;
  productId?: string;
  name: string;
  platform: "facebook" | "google" | "tiktok" | "custom";
  pixelId: string;
  events: string[];
  isActive: boolean;
  settings?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  product?: {
    id: string;
    name: string;
  };
  organization?: {
    id: string;
    name: string;
  };
}

export interface CreatePixelData {
  organizationId: string;
  productId?: string;
  name: string;
  platform: "facebook" | "google" | "tiktok" | "custom";
  pixelId: string;
  events?: string[];
  isActive?: boolean;
  settings?: Record<string, any>;
}

export interface UpdatePixelData {
  name?: string;
  platform?: "facebook" | "google" | "tiktok" | "custom";
  pixelId?: string;
  events?: string[];
  isActive?: boolean;
  settings?: Record<string, any>;
  productId?: string;
}

// API Client
class PixelsApiClient {
  async getPixelsByOrganization(organizationId: string): Promise<{ pixels: Pixel[] }> {
    const response = await fetch(`/api/pixels/organization/${organizationId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pixels: ${response.statusText}`);
    }
    return response.json();
  }

  async getPixelsByProduct(productId: string): Promise<{ pixels: Pixel[] }> {
    const response = await fetch(`/api/pixels/product/${productId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pixels: ${response.statusText}`);
    }
    return response.json();
  }

  async getPixel(id: string): Promise<{ pixel: Pixel }> {
    const response = await fetch(`/api/pixels/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pixel: ${response.statusText}`);
    }
    return response.json();
  }

  async createPixel(data: CreatePixelData): Promise<{ pixel: Pixel }> {
    const response = await fetch("/api/pixels", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: response.statusText }));
      throw new Error(error.message || "Failed to create pixel");
    }

    return response.json();
  }

  async updatePixel(id: string, data: UpdatePixelData): Promise<{ pixel: Pixel }> {
    const response = await fetch(`/api/pixels/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: response.statusText }));
      throw new Error(error.message || "Failed to update pixel");
    }

    return response.json();
  }

  async deletePixel(id: string): Promise<{ message: string }> {
    const response = await fetch(`/api/pixels/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: response.statusText }));
      throw new Error(error.message || "Failed to delete pixel");
    }

    return response.json();
  }
}

const apiClient = new PixelsApiClient();

// React Query Hooks
export function usePixelsByOrganization(organizationId: string) {
  return useQuery({
    queryKey: ["pixels", "organization", organizationId],
    queryFn: () => apiClient.getPixelsByOrganization(organizationId),
    enabled: !!organizationId,
    select: (data) => data.pixels,
  });
}

export function usePixelsByProduct(productId: string) {
  return useQuery({
    queryKey: ["pixels", "product", productId],
    queryFn: () => apiClient.getPixelsByProduct(productId),
    enabled: !!productId,
    select: (data) => data.pixels,
  });
}

export function usePixel(id: string) {
  return useQuery({
    queryKey: ["pixels", "detail", id],
    queryFn: () => apiClient.getPixel(id),
    enabled: !!id,
    select: (data) => data.pixel,
  });
}

export function useCreatePixel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.createPixel,
    onSuccess: (data, variables) => {
      // Invalidate pixels list for the organization
      queryClient.invalidateQueries({
        queryKey: ["pixels", "organization", variables.organizationId],
      });

      // If productId is provided, invalidate product pixels too
      if (variables.productId) {
        queryClient.invalidateQueries({
          queryKey: ["pixels", "product", variables.productId],
        });
      }

      // Set the new pixel in cache
      queryClient.setQueryData(["pixels", "detail", data.pixel.id], data.pixel);

      toast.success("Pixel criado com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao criar pixel: ${error.message}`);
    },
  });
}

export function useUpdatePixel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePixelData }) =>
      apiClient.updatePixel(id, data),
    onSuccess: (data, variables) => {
      // Update pixel in cache
      queryClient.setQueryData(["pixels", "detail", variables.id], data.pixel);

      // Invalidate all pixels lists
      queryClient.invalidateQueries({
        queryKey: ["pixels", "organization"],
      });
      queryClient.invalidateQueries({
        queryKey: ["pixels", "product"],
      });

      toast.success("Pixel atualizado com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao atualizar pixel: ${error.message}`);
    },
  });
}

export function useDeletePixel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.deletePixel,
    onSuccess: (_, pixelId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: ["pixels", "detail", pixelId],
      });

      // Invalidate all pixels lists
      queryClient.invalidateQueries({
        queryKey: ["pixels", "organization"],
      });
      queryClient.invalidateQueries({
        queryKey: ["pixels", "product"],
      });

      toast.success("Pixel excluído com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao excluir pixel: ${error.message}`);
    },
  });
}

// Utility hooks
export function useActivePixels(organizationId: string) {
  const { data: pixels, ...rest } = usePixelsByOrganization(organizationId);
  
  return {
    ...rest,
    data: pixels?.filter(pixel => pixel.isActive) || [],
  };
}

export function usePixelsByPlatform(organizationId: string, platform: Pixel["platform"]) {
  const { data: pixels, ...rest } = usePixelsByOrganization(organizationId);
  
  return {
    ...rest,
    data: pixels?.filter(pixel => pixel.platform === platform) || [],
  };
}

// Helper functions
export function getPlatformLabel(platform: Pixel["platform"]): string {
  const labels = {
    facebook: "Facebook",
    google: "Google",
    tiktok: "TikTok",
    custom: "Personalizado",
  };
  return labels[platform];
}

export function getPlatformColor(platform: Pixel["platform"]): string {
  const colors = {
    facebook: "bg-blue-100 text-blue-800",
    google: "bg-green-100 text-green-800",
    tiktok: "bg-pink-100 text-pink-800",
    custom: "bg-gray-100 text-gray-800",
  };
  return colors[platform];
}

export function getDefaultEvents(platform: Pixel["platform"]): string[] {
  const defaultEvents = {
    facebook: ["PageView", "Purchase", "AddToCart", "InitiateCheckout"],
    google: ["page_view", "purchase", "add_to_cart", "begin_checkout"],
    tiktok: ["PageView", "CompletePayment", "AddToCart", "InitiateCheckout"],
    custom: [],
  };
  return defaultEvents[platform];
}
