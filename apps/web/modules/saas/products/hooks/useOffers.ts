"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
export interface Offer {
  id: string;
  productId: string;
  name: string;
  type: "single-price" | "subscription" | "ORDER_BUMP" | "UPSELL" | "DOWNSELL";
  valueCents: number;
  currency: string;
  isActive: boolean;
  startsAt?: string;
  expiresAt?: string;
  targetProductId?: string;
  settings?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  product?: {
    id: string;
    name: string;
  };
  targetProduct?: {
    id: string;
    name: string;
  };
}

export interface CreateOfferData {
  productId: string;
  name: string;
  type: "single-price" | "subscription" | "ORDER_BUMP" | "UPSELL" | "DOWNSELL";
  valueCents: number;
  currency?: string;
  isActive?: boolean;
  startsAt?: string;
  expiresAt?: string;
  targetProductId?: string;
  settings?: Record<string, any>;
}

export interface UpdateOfferData {
  name?: string;
  type?: "single-price" | "subscription" | "ORDER_BUMP" | "UPSELL" | "DOWNSELL";
  valueCents?: number;
  currency?: string;
  isActive?: boolean;
  startsAt?: string;
  expiresAt?: string;
  targetProductId?: string;
  settings?: Record<string, any>;
}

// API Client
class OffersApiClient {
  async getOffers(productId: string): Promise<{ offers: Offer[] }> {
    const response = await fetch(`/api/offers/product/${productId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch offers: ${response.statusText}`);
    }
    return response.json();
  }

  async getOffer(id: string): Promise<{ offer: Offer }> {
    const response = await fetch(`/api/offers/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch offer: ${response.statusText}`);
    }
    return response.json();
  }

  async createOffer(data: CreateOfferData): Promise<{ offer: Offer }> {
    const response = await fetch("/api/offers", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: response.statusText }));
      throw new Error(error.message || "Failed to create offer");
    }

    return response.json();
  }

  async updateOffer(id: string, data: UpdateOfferData): Promise<{ offer: Offer }> {
    const response = await fetch(`/api/offers/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: response.statusText }));
      throw new Error(error.message || "Failed to update offer");
    }

    return response.json();
  }

  async deleteOffer(id: string): Promise<{ message: string }> {
    const response = await fetch(`/api/offers/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: response.statusText }));
      throw new Error(error.message || "Failed to delete offer");
    }

    return response.json();
  }
}

const apiClient = new OffersApiClient();

// React Query Hooks
export function useOffers(productId: string) {
  return useQuery({
    queryKey: ["offers", "list", productId],
    queryFn: () => apiClient.getOffers(productId),
    enabled: !!productId,
    select: (data) => data.offers,
  });
}

export function useOffer(id: string) {
  return useQuery({
    queryKey: ["offers", "detail", id],
    queryFn: () => apiClient.getOffer(id),
    enabled: !!id,
    select: (data) => data.offer,
  });
}

export function useCreateOffer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.createOffer,
    onSuccess: (data, variables) => {
      // Invalidate offers list for the product
      queryClient.invalidateQueries({
        queryKey: ["offers", "list", variables.productId],
      });

      // Set the new offer in cache
      queryClient.setQueryData(["offers", "detail", data.offer.id], data.offer);

      toast.success("Oferta criada com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao criar oferta: ${error.message}`);
    },
  });
}

export function useUpdateOffer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateOfferData }) =>
      apiClient.updateOffer(id, data),
    onSuccess: (data, variables) => {
      // Update the offer in cache
      queryClient.setQueryData(["offers", "detail", variables.id], data.offer);

      // Invalidate offers list for the product
      if (data.offer.productId) {
        queryClient.invalidateQueries({
          queryKey: ["offers", "list", data.offer.productId],
        });
      }

      toast.success("Oferta atualizada com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao atualizar oferta: ${error.message}`);
    },
  });
}

export function useDeleteOffer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.deleteOffer,
    onSuccess: (_, offerId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: ["offers", "detail", offerId],
      });

      // Invalidate all offers lists
      queryClient.invalidateQueries({
        queryKey: ["offers", "list"],
      });

      toast.success("Oferta excluída com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao excluir oferta: ${error.message}`);
    },
  });
}

// Utility hooks
export function useOffersByType(productId: string, type: Offer["type"]) {
  const { data: offers, ...rest } = useOffers(productId);
  
  return {
    ...rest,
    data: offers?.filter(offer => offer.type === type) || [],
  };
}

export function useActiveOffers(productId: string) {
  const { data: offers, ...rest } = useOffers(productId);
  
  return {
    ...rest,
    data: offers?.filter(offer => offer.isActive) || [],
  };
}

// Helper functions
export function formatOfferPrice(offer: Offer): string {
  const price = offer.valueCents / 100;
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: offer.currency,
  }).format(price);
}

export function isOfferExpired(offer: Offer): boolean {
  if (!offer.expiresAt) return false;
  return new Date(offer.expiresAt) < new Date();
}

export function isOfferActive(offer: Offer): boolean {
  if (!offer.isActive) return false;
  if (isOfferExpired(offer)) return false;
  if (offer.startsAt && new Date(offer.startsAt) > new Date()) return false;
  return true;
}
