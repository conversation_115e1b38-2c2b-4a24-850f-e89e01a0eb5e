import { useQuery } from "@tanstack/react-query";

export interface AnalyticsMetrics {
  totalRevenue: number;
  totalTransactions: number;
  activeUsers: number;
  conversionRate: number;
}

export interface MonthlyRevenueData {
  month: string;
  revenue: number;
  transactions: number;
}

export interface PaymentMethodData {
  method: string;
  percentage: number;
  count: number;
  value: number;
}

export interface TopProductData {
  id: string;
  name: string;
  revenue: number;
  sales: number;
}

export interface RecentTransaction {
  id: string;
  customer: string;
  product: string;
  amount: number;
  method: string;
  status: string;
  date: string;
}

export interface AnalyticsData {
  metrics: AnalyticsMetrics;
  charts: {
    monthlyRevenue: MonthlyRevenueData[];
    paymentMethods: PaymentMethodData[];
    topProducts: TopProductData[];
  };
  recentTransactions: RecentTransaction[];
  period: {
    from: string;
    to: string;
    type: string;
  };
}

export interface AnalyticsFilters {
  period?: "7d" | "30d" | "90d" | "1y" | "all";
  startDate?: string;
  endDate?: string;
}

export function useAnalytics(organizationId: string, filters: AnalyticsFilters = {}) {
  return useQuery({
    queryKey: ["analytics", organizationId, filters],
    queryFn: async (): Promise<AnalyticsData> => {
      const params = new URLSearchParams();

      if (filters.period) params.append("period", filters.period);
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);

      const response = await fetch(`/api/analytics/organization/${organizationId}?${params.toString()}`, {
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch analytics data");
      }

      return response.json();
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
}
