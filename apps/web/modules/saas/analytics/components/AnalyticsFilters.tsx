"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Calendar, Download, Filter } from "lucide-react";
import { AnalyticsFilters } from "../hooks/useAnalytics";

interface AnalyticsFiltersComponentProps {
  filters: AnalyticsFilters;
  onFiltersChange: (filters: AnalyticsFilters) => void;
  onExport: () => void;
  isLoading?: boolean;
}

const PERIOD_OPTIONS = [
  { value: "7d", label: "Últimos 7 dias" },
  { value: "30d", label: "Últimos 30 dias" },
  { value: "90d", label: "Últimos 90 dias" },
  { value: "1y", label: "Último ano" },
  { value: "all", label: "Todo o período" },
];

export function AnalyticsFiltersComponent({
  filters,
  onFiltersChange,
  onExport,
  isLoading = false,
}: AnalyticsFiltersComponentProps) {
  const [isCustomDateOpen, setIsCustomDateOpen] = useState(false);

  const handlePeriodChange = (period: string) => {
    onFiltersChange({
      ...filters,
      period: period as AnalyticsFilters["period"],
      startDate: undefined,
      endDate: undefined,
    });
  };

  const getPeriodLabel = () => {
    const option = PERIOD_OPTIONS.find(opt => opt.value === filters.period);
    return option?.label || "Selecionar período";
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Período:</span>
            </div>

            <Select value={filters.period || "30d"} onValueChange={handlePeriodChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Selecionar período" />
              </SelectTrigger>
              <SelectContent>
                {PERIOD_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsCustomDateOpen(!isCustomDateOpen)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filtros
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
              disabled={isLoading}
            >
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>
        </div>

        {isCustomDateOpen && (
          <div className="mt-4 p-4 border rounded-lg bg-muted/50">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Data inicial
                </label>
                <input
                  type="date"
                  value={filters.startDate || ""}
                  onChange={(e) =>
                    onFiltersChange({
                      ...filters,
                      startDate: e.target.value,
                      period: undefined,
                    })
                  }
                  className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm"
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Data final
                </label>
                <input
                  type="date"
                  value={filters.endDate || ""}
                  onChange={(e) =>
                    onFiltersChange({
                      ...filters,
                      endDate: e.target.value,
                      period: undefined,
                    })
                  }
                  className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm"
                />
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
