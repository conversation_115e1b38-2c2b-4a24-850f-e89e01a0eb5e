"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import {
  DollarSign,
  CreditCard,
  Users,
  TrendingUp,
  TrendingDown,
  Minus,
} from "lucide-react";
import { AnalyticsMetrics as MetricsData } from "../hooks/useAnalytics";

interface MetricCardProps {
  title: string;
  value: string;
  change?: string;
  isPositive?: boolean;
  icon: React.ComponentType<{ className?: string }>;
  isLoading?: boolean;
}

function MetricCard({ title, value, change, isPositive, icon: Icon, isLoading }: MetricCardProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <Icon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-24 mb-2" />
          <Skeleton className="h-4 w-16" />
        </CardContent>
      </Card>
    );
  }

  const getChangeIcon = () => {
    if (!change) return <Minus className="h-3 w-3" />;
    return isPositive ? (
      <TrendingUp className="h-3 w-3" />
    ) : (
      <TrendingDown className="h-3 w-3" />
    );
  };

  const getChangeColor = () => {
    if (!change) return "text-muted-foreground";
    return isPositive ? "text-green-600" : "text-red-600";
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <div className={`flex items-center gap-1 text-xs ${getChangeColor()}`}>
            {getChangeIcon()}
            <span>{change}</span>
            <span className="text-muted-foreground">vs período anterior</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface AnalyticsMetricsProps {
  data?: MetricsData;
  isLoading?: boolean;
}

export function AnalyticsMetrics({ data, isLoading }: AnalyticsMetricsProps) {
  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(cents / 100);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("pt-BR").format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const metrics = [
    {
      title: "Receita Total",
      value: data ? formatCurrency(data.totalRevenue) : "R$ 0,00",
      change: data ? "+12.5%" : undefined,
      isPositive: true,
      icon: DollarSign,
    },
    {
      title: "Transações",
      value: data ? formatNumber(data.totalTransactions) : "0",
      change: data ? "+8.2%" : undefined,
      isPositive: true,
      icon: CreditCard,
    },
    {
      title: "Usuários Ativos",
      value: data ? formatNumber(data.activeUsers) : "0",
      change: data ? "+15.3%" : undefined,
      isPositive: true,
      icon: Users,
    },
    {
      title: "Taxa de Conversão",
      value: data ? formatPercentage(data.conversionRate) : "0%",
      change: data ? "+2.1%" : undefined,
      isPositive: true,
      icon: TrendingUp,
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          change={metric.change}
          isPositive={metric.isPositive}
          icon={metric.icon}
          isLoading={isLoading}
        />
      ))}
    </div>
  );
}
