"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@ui/components/chart";
import { Area, AreaChart, Bar, BarChart, CartesianGrid, Pie, Pie<PERSON>hart, ResponsiveContainer, XAxis, YAxis } from "recharts";
import { MonthlyRevenueData, PaymentMethodData, TopProductData } from "../hooks/useAnalytics";

interface RevenueChartProps {
  data: MonthlyRevenueData[];
  className?: string;
}

export function RevenueChart({ data, className }: RevenueChartProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Receita Mensal</CardTitle>
        <CardDescription>
          Evolução da receita ao longo do tempo
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            revenue: {
              label: "<PERSON><PERSON><PERSON>",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="min-h-[300px] w-full"
        >
          <AreaChart data={data} accessibilityLayer>
            <defs>
              <linearGradient id="fillRevenue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#4e6df5" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#4e6df5" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={10}
              className="text-xs fill-muted-foreground"
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={10}
              tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
              className="text-xs fill-muted-foreground"
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => `Mês: ${value}`}
                  formatter={(value) => [
                    `R$ ${Number(value).toLocaleString()}`,
                    "Receita"
                  ]}
                />
              }
            />
            <Area
              type="monotone"
              dataKey="revenue"
              stroke="#4e6df5"
              fill="url(#fillRevenue)"
              strokeWidth={2}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

interface TransactionsChartProps {
  data: MonthlyRevenueData[];
  className?: string;
}

export function TransactionsChart({ data, className }: TransactionsChartProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Transações Mensais</CardTitle>
        <CardDescription>
          Volume de transações processadas por mês
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            transactions: {
              label: "Transações",
              color: "hsl(var(--chart-2))",
            },
          }}
          className="min-h-[300px] w-full"
        >
          <BarChart data={data} accessibilityLayer>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={10}
              className="text-xs fill-muted-foreground"
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={10}
              className="text-xs fill-muted-foreground"
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => `Mês: ${value}`}
                  formatter={(value) => [value, "Transações"]}
                />
              }
            />
            <Bar
              dataKey="transactions"
              fill="#39a561"
              radius={4}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

interface PaymentMethodsChartProps {
  data: PaymentMethodData[];
  className?: string;
}

export function PaymentMethodsChart({ data, className }: PaymentMethodsChartProps) {
  const COLORS = {
    PIX: "hsl(var(--chart-1))",
    "Cartão de Crédito": "hsl(var(--chart-2))",
    Boleto: "hsl(var(--chart-3))",
    "Cartão de Débito": "hsl(var(--chart-4))",
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Métodos de Pagamento</CardTitle>
        <CardDescription>
          Distribuição por método de pagamento
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            value: {
              label: "Valor",
            },
            PIX: {
              label: "PIX",
              color: "hsl(var(--chart-1))",
            },
            "Cartão de Crédito": {
              label: "Cartão de Crédito",
              color: "hsl(var(--chart-2))",
            },
            Boleto: {
              label: "Boleto",
              color: "hsl(var(--chart-3))",
            },
            "Cartão de Débito": {
              label: "Cartão de Débito",
              color: "hsl(var(--chart-4))",
            },
          }}
          className="min-h-[300px] w-full"
        >
          <PieChart data={data} accessibilityLayer>
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name, props) => [
                    `R$ ${Number(value).toLocaleString()}`,
                    props.payload.method
                  ]}
                />
              }
            />
            <Pie
              data={data.map((item, index) => ({
                ...item,
                fill: ["#4e6df5", "#39a561", "#e5a158", "#ef4444", "#8b5cf6"][index % 5]
              }))}
              dataKey="value"
              nameKey="method"
              cx="50%"
              cy="50%"
              outerRadius={80}
              label={({ method, percentage }) => `${method}: ${percentage}%`}
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

interface TopProductsChartProps {
  data: TopProductData[];
  className?: string;
}

export function TopProductsChart({ data, className }: TopProductsChartProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Produtos Mais Vendidos</CardTitle>
        <CardDescription>
          Ranking dos produtos com melhor performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((product, index) => (
            <div key={product.id} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-sm font-medium">
                  {index + 1}
                </div>
                <div>
                  <p className="font-medium">{product.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {product.sales} vendas
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold">
                  R$ {(product.revenue / 100).toLocaleString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
