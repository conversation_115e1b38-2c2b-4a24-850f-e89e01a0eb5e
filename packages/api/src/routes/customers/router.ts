import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

export const customersRouter = new Hono()
  .basePath("/customers")
  .get(
    "/",
    authMiddleware,
    validator(
      "query",
      z.object({
        organizationId: z.string(),
        page: z.string().optional().default("1"),
        limit: z.string().optional().default("10"),
        search: z.string().optional(),
        status: z.string().optional(),
        sortBy: z.string().optional().default("createdAt"),
        sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
      }),
    ),
    describeRoute({
      tags: ["Customers"],
      summary: "Get customers",
      description: "Get paginated list of customers for an organization",
      responses: {
        200: {
          description: "Customers list with pagination",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  customers: z.array(
                    z.object({
                      id: z.string(),
                      name: z.string(),
                      email: z.string(),
                      image: z.string().nullable(),
                      createdAt: z.date(),
                      updatedAt: z.date(),
                      status: z.string(),
                      paymentsCustomerId: z.string().nullable(),
                      locale: z.string().nullable(),
                      totalSpent: z.number(),
                      lastPurchase: z.date().nullable(),
                      purchaseCount: z.number(),
                    })
                  ),
                  pagination: z.object({
                    page: z.number(),
                    limit: z.number(),
                    total: z.number(),
                    totalPages: z.number(),
                    hasNext: z.boolean(),
                    hasPrev: z.boolean(),
                  }),
                })
              ),
            },
          },
        },
      },
    }),
    async (c) => {
      const {
        organizationId,
        page,
        limit,
        search,
        status,
        sortBy,
        sortOrder,
      } = c.req.valid("query");

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;

      try {
        // Construir filtros
        const where: any = {
          organizationMembers: {
            some: {
              organizationId,
              role: "CUSTOMER",
            },
          },
        };

        // Filtro de busca
        if (search) {
          where.OR = [
            { name: { contains: search, mode: "insensitive" } },
            { email: { contains: search, mode: "insensitive" } },
          ];
        }

        // Filtro de status
        if (status) {
          if (status === "active") {
            where.banned = { not: true };
          } else if (status === "inactive") {
            where.banned = true;
          }
        }

        // Buscar clientes
        const [customers, total] = await Promise.all([
          db.user.findMany({
            where,
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              createdAt: true,
              updatedAt: true,
              banned: true,
              paymentsCustomerId: true,
              locale: true,
          orders: {
            select: {
              id: true,
              totalCents: true,
              currency: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: "desc",
            },
          },
            },
            orderBy: {
              [sortBy]: sortOrder,
            },
            skip,
            take: limitNum,
          }),
          db.user.count({ where }),
        ]);

        // Calcular métricas para cada cliente
        const customersWithMetrics = customers.map((customer) => {
          const totalSpent = customer.orders.reduce(
            (sum, order) => sum + order.totalCents,
            0
          );
          const lastPurchase = customer.orders[0]?.createdAt || null;
          const purchaseCount = customer.orders.length;

          return {
            id: customer.id,
            name: customer.name || "Cliente sem nome",
            email: customer.email,
            image: customer.image,
            createdAt: customer.createdAt,
            updatedAt: customer.updatedAt,
            status: customer.banned ? "inactive" : "active",
            paymentsCustomerId: customer.paymentsCustomerId,
            locale: customer.locale,
            totalSpent,
            lastPurchase,
            purchaseCount,
          };
        });

        const totalPages = Math.ceil(total / limitNum);

        return c.json({
          customers: customersWithMetrics,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total,
            totalPages,
            hasNext: pageNum < totalPages,
            hasPrev: pageNum > 1,
          },
        });
      } catch (error) {
        logger.error("Error fetching customers:", error);
        throw new HTTPException(500, { message: "Failed to fetch customers" });
      }
    }
  )
  .get(
    "/metrics",
    authMiddleware,
    validator(
      "query",
      z.object({
        organizationId: z.string(),
        period: z.string().optional().default("30d"),
      }),
    ),
    describeRoute({
      tags: ["Customers"],
      summary: "Get customer metrics",
      description: "Get customer metrics for an organization",
      responses: {
        200: {
          description: "Customer metrics",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  totalCustomers: z.number(),
                  activeCustomers: z.number(),
                  inactiveCustomers: z.number(),
                  newCustomers: z.number(),
                  totalRevenueCents: z.number(),
                  averageOrderValueCents: z.number(),
                  customerGrowth: z.number(),
                  period: z.string(),
                })
              ),
            },
          },
        },
      },
    }),
    async (c) => {
      const { organizationId, period } = c.req.valid("query");

      try {
        // Calcular datas baseadas no período
        const now = new Date();
        let startDate: Date;

        switch (period) {
          case "7d":
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case "30d":
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case "90d":
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          case "1y":
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        }

        // Buscar métricas em paralelo
        const [
          totalCustomers,
          activeCustomers,
          newCustomers,
          totalRevenue,
          averageOrderValue,
        ] = await Promise.all([
          // Total de clientes
          db.user.count({
            where: {
              organizationMembers: {
                some: {
                  organizationId,
                  role: "CUSTOMER",
                },
              },
            },
          }),

          // Clientes ativos (não banidos)
          db.user.count({
            where: {
              organizationMembers: {
                some: {
                  organizationId,
                  role: "CUSTOMER",
                },
              },
              banned: { not: true },
            },
          }),

          // Novos clientes no período
          db.user.count({
            where: {
              organizationMembers: {
                some: {
                  organizationId,
                  role: "CUSTOMER",
                },
              },
              createdAt: {
                gte: startDate,
              },
            },
          }),

          // Receita total no período
          db.order.aggregate({
            where: {
              organizationId,
              createdAt: {
                gte: startDate,
              },
            },
            _sum: {
              totalCents: true,
            },
          }),

          // Valor médio do pedido
          db.order.aggregate({
            where: {
              organizationId,
              createdAt: {
                gte: startDate,
              },
            },
            _avg: {
              totalCents: true,
            },
          }),
        ]);

        // Calcular métricas derivadas
        const totalRevenueCents = totalRevenue._sum.totalCents || 0;
        const averageOrderValueCents = averageOrderValue._avg.totalCents || 0;
        const inactiveCustomers = totalCustomers - activeCustomers;

        // Calcular crescimento de clientes
        const previousStartDate = new Date(
          startDate.getTime() - (now.getTime() - startDate.getTime())
        );
        const previousNewCustomers = await db.user.count({
          where: {
            organizationMembers: {
              some: {
                organizationId,
                role: "CUSTOMER",
              },
            },
            createdAt: {
              gte: previousStartDate,
              lt: startDate,
            },
          },
        });

        const customerGrowth =
          previousNewCustomers > 0
            ? ((newCustomers - previousNewCustomers) / previousNewCustomers) * 100
            : 0;

        return c.json({
          totalCustomers,
          activeCustomers,
          inactiveCustomers,
          newCustomers,
          totalRevenueCents,
          averageOrderValueCents,
          customerGrowth,
          period,
        });
      } catch (error) {
        logger.error("Error fetching customer metrics:", error);
        throw new HTTPException(500, { message: "Failed to fetch customer metrics" });
      }
    }
  )
  .post(
    "/",
    authMiddleware,
    validator(
      "json",
      z.object({
        organizationId: z.string(),
        name: z.string().min(1, "Nome é obrigatório"),
        email: z.string().email("Email inválido"),
        phone: z.string().optional(),
        city: z.string().optional(),
        state: z.string().optional(),
        document: z.string().optional(),
        birthDate: z.string().optional(),
        address: z.string().optional(),
        zipCode: z.string().optional(),
        locale: z.string().optional().default("pt-BR"),
      }),
    ),
    describeRoute({
      tags: ["Customers"],
      summary: "Create customer",
      description: "Create a new customer for an organization",
      responses: {
        200: {
          description: "Created customer",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  id: z.string(),
                  name: z.string(),
                  email: z.string(),
                  image: z.string().nullable(),
                  createdAt: z.date(),
                  status: z.string(),
                  locale: z.string().nullable(),
                })
              ),
            },
          },
        },
      },
    }),
    async (c) => {
      const data = c.req.valid("json");

      try {
        // Verificar se o email já existe
        const existingUser = await db.user.findUnique({
          where: { email: data.email },
        });

        if (existingUser) {
          throw new HTTPException(400, { message: "Email já está em uso" });
        }

        // Criar usuário
        const user = await db.user.create({
          data: {
            name: data.name,
            email: data.email,
            image: null,
            locale: data.locale,
            role: "CUSTOMER",
            onboardingComplete: true,
          },
        });

        // Adicionar como membro da organização
        await db.member.create({
          data: {
            organizationId: data.organizationId,
            userId: user.id,
            role: "CUSTOMER",
            createdAt: new Date(),
          },
        });

        return c.json({
          id: user.id,
          name: user.name,
          email: user.email,
          image: user.image,
          createdAt: user.createdAt,
          status: "active",
          locale: user.locale,
        });
      } catch (error) {
        if (error instanceof HTTPException) {
          throw error;
        }
        logger.error("Error creating customer:", error);
        throw new HTTPException(500, { message: "Failed to create customer" });
      }
    }
  );
