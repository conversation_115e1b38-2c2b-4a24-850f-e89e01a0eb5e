import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { HTTPException } from "hono/http-exception";

// Validation schemas
const createOfferSchema = z.object({
  productId: z.string(),
  name: z.string().min(1, "Name is required").max(100, "Name must be at most 100 characters"),
  type: z.enum(["single-price", "subscription", "ORDER_BUMP", "UPSELL", "DOWNSELL"]),
  valueCents: z.number().min(0, "Value must be greater than or equal to 0"),
  currency: z.string().default("BRL"),
  isActive: z.boolean().default(true),
  startsAt: z.string().datetime().optional(),
  expiresAt: z.string().datetime().optional(),
  targetProductId: z.string().optional(),
  settings: z.record(z.any()).optional(),
});

const updateOfferSchema = createOfferSchema.partial().omit({ productId: true });

const offerParamsSchema = z.object({
  id: z.string(),
});

const productParamsSchema = z.object({
  productId: z.string(),
});

export const offersRouter = new Hono()
  .basePath("/offers")
  .use("*", authMiddleware)

  // GET /api/offers/product/:productId - Get all offers for a product
  .get(
    "/product/:productId",
    validator("param", productParamsSchema),
    describeRoute({
      tags: ["Offers"],
      summary: "Get product offers",
      description: "Get all offers for a specific product",
      responses: {
        200: {
          description: "List of offers",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const { productId } = c.req.valid("param");
      const user = c.get("user");

      // Verify product exists and user has access
      const product = await db.product.findFirst({
        where: {
          id: productId,
          organization: {
            members: {
              some: {
                userId: user.id,
              },
            },
          },
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Product not found" });
      }

      const offers = await db.offer.findMany({
        where: {
          productId,
        },
        include: {
          targetProduct: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({ offers });
    },
  )

  // GET /api/offers/:id - Get specific offer
  .get(
    "/:id",
    validator("param", offerParamsSchema),
    describeRoute({
      tags: ["Offers"],
      summary: "Get offer by ID",
      description: "Get a specific offer by ID",
      responses: {
        200: {
          description: "Offer details",
        },
        404: {
          description: "Offer not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const user = c.get("user");

      const offer = await db.offer.findFirst({
        where: {
          id,
          product: {
            organization: {
              members: {
                some: {
                  userId: user.id,
                },
              },
            },
          },
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              organizationId: true,
            },
          },
          targetProduct: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!offer) {
        throw new HTTPException(404, { message: "Offer not found" });
      }

      return c.json({ offer });
    },
  )

  // POST /api/offers - Create new offer
  .post(
    "/",
    validator("json", createOfferSchema),
    describeRoute({
      tags: ["Offers"],
      summary: "Create offer",
      description: "Create a new offer for a product",
      responses: {
        201: {
          description: "Offer created successfully",
        },
        400: {
          description: "Invalid input data",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const data = c.req.valid("json");

      // Verify product exists and user has access
      const product = await db.product.findFirst({
        where: {
          id: data.productId,
          organization: {
            members: {
              some: {
                userId: user.id,
              },
            },
          },
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Product not found" });
      }

      // If targetProductId is provided, verify it exists and user has access
      if (data.targetProductId) {
        const targetProduct = await db.product.findFirst({
          where: {
            id: data.targetProductId,
            organization: {
              members: {
                some: {
                  userId: user.id,
                },
              },
            },
          },
        });

        if (!targetProduct) {
          throw new HTTPException(400, { message: "Target product not found" });
        }
      }

      const offer = await db.offer.create({
        data: {
          ...data,
          startsAt: data.startsAt ? new Date(data.startsAt) : null,
          expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
            },
          },
          targetProduct: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return c.json({ offer }, 201);
    },
  )

  // PUT /api/offers/:id - Update offer
  .put(
    "/:id",
    validator("param", offerParamsSchema),
    validator("json", updateOfferSchema),
    describeRoute({
      tags: ["Offers"],
      summary: "Update offer",
      description: "Update an existing offer",
      responses: {
        200: {
          description: "Offer updated successfully",
        },
        400: {
          description: "Invalid input data",
        },
        404: {
          description: "Offer not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const user = c.get("user");
      const data = c.req.valid("json");

      // Verify offer exists and user has access
      const existingOffer = await db.offer.findFirst({
        where: {
          id,
          product: {
            organization: {
              members: {
                some: {
                  userId: user.id,
                },
              },
            },
          },
        },
      });

      if (!existingOffer) {
        throw new HTTPException(404, { message: "Offer not found" });
      }

      // If targetProductId is being updated, verify it exists and user has access
      if (data.targetProductId) {
        const targetProduct = await db.product.findFirst({
          where: {
            id: data.targetProductId,
            organization: {
              members: {
                some: {
                  userId: user.id,
                },
              },
            },
          },
        });

        if (!targetProduct) {
          throw new HTTPException(400, { message: "Target product not found" });
        }
      }

      const offer = await db.offer.update({
        where: { id },
        data: {
          ...data,
          startsAt: data.startsAt ? new Date(data.startsAt) : undefined,
          expiresAt: data.expiresAt ? new Date(data.expiresAt) : undefined,
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
            },
          },
          targetProduct: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return c.json({ offer });
    },
  )

  // DELETE /api/offers/:id - Delete offer
  .delete(
    "/:id",
    validator("param", offerParamsSchema),
    describeRoute({
      tags: ["Offers"],
      summary: "Delete offer",
      description: "Delete an offer",
      responses: {
        200: {
          description: "Offer deleted successfully",
        },
        404: {
          description: "Offer not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const user = c.get("user");

      // Verify offer exists and user has access
      const offer = await db.offer.findFirst({
        where: {
          id,
          product: {
            organization: {
              members: {
                some: {
                  userId: user.id,
                },
              },
            },
          },
        },
      });

      if (!offer) {
        throw new HTTPException(404, { message: "Offer not found" });
      }

      await db.offer.delete({
        where: { id },
      });

      return c.json({ message: "Offer deleted successfully" });
    },
  );
