import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";

export const analyticsRouter = new Hono()
  .basePath("/analytics")
  .use(authMiddleware)

  // GET /api/analytics/organization/:organizationId - Analytics da organização
  .get(
    "/organization/:organizationId",
    validator(
      "param",
      z.object({
        organizationId: z.string(),
      }),
    ),
    validator(
      "query",
      z.object({
        period: z.enum(["7d", "30d", "90d", "1y", "all"]).optional().default("30d"),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
      }),
    ),
    describeRoute({
      tags: ["Analytics"],
      summary: "Get organization analytics",
      description: "Get comprehensive analytics data for an organization",
      responses: {
        200: {
          description: "Analytics data",
        },
        404: {
          description: "Organization not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { organizationId } = c.req.valid("param");
      const { period, startDate, endDate } = c.req.valid("query");

      // Verificar se o usuário tem acesso à organização
      const membership = await db.member.findFirst({
        where: {
          organizationId,
          userId: user.id,
        },
      });

      if (!membership) {
        throw new HTTPException(403, { message: "Acesso negado à organização" });
      }

      // Calcular datas baseado no período
      const now = new Date();
      let dateFrom: Date;
      let dateTo: Date = now;

      if (startDate && endDate) {
        dateFrom = new Date(startDate);
        dateTo = new Date(endDate);
      } else {
        switch (period) {
          case "7d":
            dateFrom = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case "30d":
            dateFrom = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case "90d":
            dateFrom = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          case "1y":
            dateFrom = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
          case "all":
            dateFrom = new Date(0);
            break;
        }
      }

      // Buscar dados de analytics em paralelo
      const [
        totalRevenue,
        totalTransactions,
        activeUsers,
        topProducts,
        monthlyRevenue,
        paymentMethodsData,
        recentTransactions,
        conversionRate,
      ] = await Promise.all([
        // Receita total
        db.order.aggregate({
          where: {
            product: {
              organizationId,
            },
            status: "COMPLETED",
            createdAt: {
              gte: dateFrom,
              lte: dateTo,
            },
          },
          _sum: {
            totalCents: true,
          },
        }),

        // Total de transações
        db.order.count({
          where: {
            product: {
              organizationId,
            },
            status: "COMPLETED",
            createdAt: {
              gte: dateFrom,
              lte: dateTo,
            },
          },
        }),

        // Usuários únicos
        db.order.groupBy({
          by: ["buyerId"],
          where: {
            product: {
              organizationId,
            },
            status: "COMPLETED",
            createdAt: {
              gte: dateFrom,
              lte: dateTo,
            },
          },
        }),

        // Top produtos
        db.order.groupBy({
          by: ["productId"],
          where: {
            product: {
              organizationId,
            },
            status: "COMPLETED",
            createdAt: {
              gte: dateFrom,
              lte: dateTo,
            },
          },
          _sum: {
            totalCents: true,
          },
          _count: {
            id: true,
          },
          orderBy: {
            _sum: {
              totalCents: "desc",
            },
          },
          take: 5,
        }),

        // Receita mensal (últimos 12 meses)
        db.order.groupBy({
          by: ["createdAt"],
          where: {
            product: {
              organizationId,
            },
            status: "COMPLETED",
            createdAt: {
              gte: new Date(now.getTime() - 12 * 30 * 24 * 60 * 60 * 1000),
            },
          },
          _sum: {
            totalCents: true,
          },
          _count: {
            id: true,
          },
        }),

        // Métodos de pagamento
        db.order.groupBy({
          by: ["paymentMethod"],
          where: {
            product: {
              organizationId,
            },
            status: "COMPLETED",
            createdAt: {
              gte: dateFrom,
              lte: dateTo,
            },
          },
          _sum: {
            totalCents: true,
          },
          _count: {
            id: true,
          },
        }),

        // Transações recentes
        db.order.findMany({
          where: {
            product: {
              organizationId,
            },
            createdAt: {
              gte: dateFrom,
              lte: dateTo,
            },
          },
          include: {
            buyer: {
              select: {
                name: true,
                email: true,
              },
            },
            product: {
              select: {
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        }),

        // Taxa de conversão (simulada por enquanto)
        db.order.count({
          where: {
            product: {
              organizationId,
            },
            status: "COMPLETED",
            createdAt: {
              gte: dateFrom,
              lte: dateTo,
            },
          },
        }),
      ]);

      // Buscar detalhes dos produtos top
      const topProductsWithDetails = await Promise.all(
        topProducts.map(async (product) => {
          const productDetails = await db.product.findUnique({
            where: { id: product.productId },
            select: { name: true },
          });
          return {
            id: product.productId,
            name: productDetails?.name || "Produto não encontrado",
            revenue: product._sum.totalCents || 0,
            sales: product._count.id,
          };
        })
      );

      // Processar dados mensais
      const monthlyData = monthlyRevenue.map((item) => {
        const date = new Date(item.createdAt);
        return {
          month: date.toLocaleDateString("pt-BR", { month: "short", year: "numeric" }),
          revenue: item._sum.totalCents || 0,
          transactions: item._count.id,
        };
      });

      // Processar métodos de pagamento
      const totalPaymentValue = paymentMethodsData.reduce((sum, item) => sum + (item._sum.totalCents || 0), 0);
      const paymentMethods = paymentMethodsData.map((item) => ({
        method: item.paymentMethod,
        percentage: totalPaymentValue > 0 ? Math.round(((item._sum.totalCents || 0) / totalPaymentValue) * 100) : 0,
        count: item._count.id,
        value: item._sum.totalCents || 0,
      }));

      // Calcular taxa de conversão (simulada)
      const conversionRateValue = totalTransactions > 0 ? (totalTransactions / (totalTransactions * 20)) * 100 : 0;

      return c.json({
        metrics: {
          totalRevenue: totalRevenue._sum.totalCents || 0,
          totalTransactions,
          activeUsers: activeUsers.length,
          conversionRate: Math.round(conversionRateValue * 100) / 100,
        },
        charts: {
          monthlyRevenue: monthlyData,
          paymentMethods,
          topProducts: topProductsWithDetails,
        },
        recentTransactions: recentTransactions.map((tx) => ({
          id: tx.id,
          customer: tx.buyer?.name || "Cliente não identificado",
          product: tx.product?.name || "Produto não encontrado",
          amount: tx.totalCents,
          method: tx.paymentMethod,
          status: tx.status,
          date: tx.createdAt,
        })),
        period: {
          from: dateFrom,
          to: dateTo,
          type: period,
        },
      });
    },
  );

export type AnalyticsRouter = typeof analyticsRouter;
