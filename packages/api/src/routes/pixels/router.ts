import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { HTTPException } from "hono/http-exception";

// Validation schemas
const createPixelSchema = z.object({
  organizationId: z.string(),
  productId: z.string().optional(),
  name: z.string().min(1, "Name is required").max(100, "Name must be at most 100 characters"),
  platform: z.enum(["facebook", "google", "tiktok", "custom"]),
  pixelId: z.string().min(1, "Pixel ID is required"),
  events: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  settings: z.record(z.any()).optional(),
});

const updatePixelSchema = createPixelSchema.partial().omit({ organizationId: true });

const pixelParamsSchema = z.object({
  id: z.string(),
});

const organizationParamsSchema = z.object({
  organizationId: z.string(),
});

const productParamsSchema = z.object({
  productId: z.string(),
});

export const pixelsRouter = new Hono()
  .basePath("/pixels")
  .use("*", authMiddleware)

  // GET /api/pixels/organization/:organizationId - Get all pixels for an organization
  .get(
    "/organization/:organizationId",
    validator("param", organizationParamsSchema),
    describeRoute({
      tags: ["Pixels"],
      summary: "Get organization pixels",
      description: "Get all pixels for a specific organization",
      responses: {
        200: {
          description: "List of pixels",
        },
        404: {
          description: "Organization not found",
        },
      },
    }),
    async (c) => {
      const { organizationId } = c.req.valid("param");
      const user = c.get("user");

      // Verify organization exists and user has access
      const organization = await db.organization.findFirst({
        where: {
          id: organizationId,
          members: {
            some: {
              userId: user.id,
            },
          },
        },
      });

      if (!organization) {
        throw new HTTPException(404, { message: "Organization not found" });
      }

      const pixels = await db.pixel.findMany({
        where: {
          organizationId,
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({ pixels });
    },
  )

  // GET /api/pixels/product/:productId - Get all pixels for a product
  .get(
    "/product/:productId",
    validator("param", productParamsSchema),
    describeRoute({
      tags: ["Pixels"],
      summary: "Get product pixels",
      description: "Get all pixels for a specific product",
      responses: {
        200: {
          description: "List of pixels",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const { productId } = c.req.valid("param");
      const user = c.get("user");

      // Verify product exists and user has access
      const product = await db.product.findFirst({
        where: {
          id: productId,
          organization: {
            members: {
              some: {
                userId: user.id,
              },
            },
          },
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Product not found" });
      }

      const pixels = await db.pixel.findMany({
        where: {
          productId,
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({ pixels });
    },
  )

  // GET /api/pixels/:id - Get pixel by ID
  .get(
    "/:id",
    validator("param", pixelParamsSchema),
    describeRoute({
      tags: ["Pixels"],
      summary: "Get pixel",
      description: "Get a specific pixel by ID",
      responses: {
        200: {
          description: "Pixel details",
        },
        404: {
          description: "Pixel not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const user = c.get("user");

      const pixel = await db.pixel.findFirst({
        where: {
          id,
          organization: {
            members: {
              some: {
                userId: user.id,
              },
            },
          },
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!pixel) {
        throw new HTTPException(404, { message: "Pixel not found" });
      }

      return c.json({ pixel });
    },
  )

  // POST /api/pixels - Create new pixel
  .post(
    "/",
    validator("json", createPixelSchema),
    describeRoute({
      tags: ["Pixels"],
      summary: "Create pixel",
      description: "Create a new pixel for tracking",
      responses: {
        201: {
          description: "Pixel created successfully",
        },
        400: {
          description: "Invalid input data",
        },
        404: {
          description: "Organization or Product not found",
        },
      },
    }),
    async (c) => {
      const data = c.req.valid("json");
      const user = c.get("user");

      // Verify organization exists and user has access
      const organization = await db.organization.findFirst({
        where: {
          id: data.organizationId,
          members: {
            some: {
              userId: user.id,
            },
          },
        },
      });

      if (!organization) {
        throw new HTTPException(404, { message: "Organization not found" });
      }

      // If productId is provided, verify product exists and belongs to organization
      if (data.productId) {
        const product = await db.product.findFirst({
          where: {
            id: data.productId,
            organizationId: data.organizationId,
          },
        });

        if (!product) {
          throw new HTTPException(404, { message: "Product not found" });
        }
      }

      const pixel = await db.pixel.create({
        data,
        include: {
          product: {
            select: {
              id: true,
              name: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return c.json({ pixel }, 201);
    },
  )

  // PUT /api/pixels/:id - Update pixel
  .put(
    "/:id",
    validator("param", pixelParamsSchema),
    validator("json", updatePixelSchema),
    describeRoute({
      tags: ["Pixels"],
      summary: "Update pixel",
      description: "Update an existing pixel",
      responses: {
        200: {
          description: "Pixel updated successfully",
        },
        400: {
          description: "Invalid input data",
        },
        404: {
          description: "Pixel not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const data = c.req.valid("json");
      const user = c.get("user");

      // Verify pixel exists and user has access
      const existingPixel = await db.pixel.findFirst({
        where: {
          id,
          organization: {
            members: {
              some: {
                userId: user.id,
              },
            },
          },
        },
      });

      if (!existingPixel) {
        throw new HTTPException(404, { message: "Pixel not found" });
      }

      // If productId is being updated, verify product exists and belongs to organization
      if (data.productId) {
        const product = await db.product.findFirst({
          where: {
            id: data.productId,
            organizationId: existingPixel.organizationId,
          },
        });

        if (!product) {
          throw new HTTPException(404, { message: "Product not found" });
        }
      }

      const pixel = await db.pixel.update({
        where: { id },
        data,
        include: {
          product: {
            select: {
              id: true,
              name: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return c.json({ pixel });
    },
  )

  // DELETE /api/pixels/:id - Delete pixel
  .delete(
    "/:id",
    validator("param", pixelParamsSchema),
    describeRoute({
      tags: ["Pixels"],
      summary: "Delete pixel",
      description: "Delete a pixel",
      responses: {
        200: {
          description: "Pixel deleted successfully",
        },
        404: {
          description: "Pixel not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const user = c.get("user");

      // Verify pixel exists and user has access
      const pixel = await db.pixel.findFirst({
        where: {
          id,
          organization: {
            members: {
              some: {
                userId: user.id,
              },
            },
          },
        },
      });

      if (!pixel) {
        throw new HTTPException(404, { message: "Pixel not found" });
      }

      await db.pixel.delete({
        where: { id },
      });

      return c.json({ message: "Pixel deleted successfully" });
    },
  );
