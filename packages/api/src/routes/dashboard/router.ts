import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";

export const dashboardRouter = new Hono()
  .basePath("/dashboard")
  .use(authMiddleware)

  // GET /api/dashboard/organization/:organizationId - Dashboard da organização
  .get(
    "/organization/:organizationId",
    validator(
      "param",
      z.object({
        organizationId: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Dashboard"],
      summary: "Get organization dashboard",
      description: "Get comprehensive dashboard data for an organization",
      responses: {
        200: {
          description: "Dashboard data",
        },
        404: {
          description: "Organization not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { organizationId } = c.req.valid("param");

      // Verificar se o usuário tem acesso à organização
      const membership = await db.member.findFirst({
        where: {
          organizationId,
          userId: user.id,
        },
      });

      if (!membership) {
        throw new HTTPException(403, { message: "Acesso negado à organização" });
      }

      // Calcular datas
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      // Buscar dados em paralelo
      const [
        // Receita total
        totalRevenue,
        monthlyRevenue,
        lastMonthRevenue,
        // Vendas
        totalSales,
        todaySales,
        yesterdaySales,
        monthlySales,
        lastMonthSales,
        // Clientes
        totalCustomers,
        activeCustomers,
        lastMonthCustomers,
        // Produtos
        totalProducts,
        publishedProducts,
        lastMonthProducts,
        lastMonthPublishedProducts,
        // Transações recentes
        recentOrders,
        // Top produtos
        topProducts,
        // Dados para gráficos
        revenueChartData,
        salesChartData,
      ] = await Promise.all([
        // Receita
        db.order.aggregate({
          where: {
            product: { organizationId },
            status: "COMPLETED",
          },
          _sum: { totalCents: true },
        }),
        db.order.aggregate({
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: thisMonthStart },
          },
          _sum: { totalCents: true },
        }),
        db.order.aggregate({
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: lastMonth, lte: lastMonthEnd },
          },
          _sum: { totalCents: true },
        }),
        // Vendas
        db.order.count({
          where: {
            product: { organizationId },
            status: "COMPLETED",
          },
        }),
        db.order.count({
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: today },
          },
        }),
        db.order.count({
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: yesterday, lt: today },
          },
        }),
        db.order.count({
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: thisMonthStart },
          },
        }),
        db.order.count({
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: lastMonth, lte: lastMonthEnd },
          },
        }),
        // Clientes
        db.order.groupBy({
          by: ["buyerId"],
          where: {
            product: { organizationId },
            status: "COMPLETED",
          },
        }),
        db.order.groupBy({
          by: ["buyerId"],
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) },
          },
        }),
        db.order.groupBy({
          by: ["buyerId"],
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: lastMonth, lte: lastMonthEnd },
          },
        }),
        // Produtos
        db.product.count({
          where: { organizationId },
        }),
        db.product.count({
          where: { organizationId, status: "PUBLISHED" },
        }),
        db.product.count({
          where: {
            organizationId,
            createdAt: { gte: lastMonth, lte: lastMonthEnd },
          },
        }),
        db.product.count({
          where: {
            organizationId,
            status: "PUBLISHED",
            createdAt: { gte: lastMonth, lte: lastMonthEnd },
          },
        }),
        // Transações recentes
        db.order.findMany({
          where: {
            product: { organizationId },
            createdAt: { gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) },
          },
          include: {
            buyer: {
              select: { name: true, email: true },
            },
            product: {
              select: { name: true },
            },
          },
          orderBy: { createdAt: "desc" },
          take: 10,
        }),
        // Top produtos
        db.order.groupBy({
          by: ["productId"],
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) },
          },
          _sum: { totalCents: true },
          _count: { id: true },
          orderBy: { _sum: { totalCents: "desc" } },
          take: 5,
        }),
        // Dados para gráficos
        db.order.groupBy({
          by: ["createdAt"],
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: new Date(now.getTime() - 12 * 30 * 24 * 60 * 60 * 1000) },
          },
          _sum: { totalCents: true },
          _count: { id: true },
        }),
        db.order.groupBy({
          by: ["createdAt"],
          where: {
            product: { organizationId },
            status: "COMPLETED",
            createdAt: { gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) },
          },
          _sum: { totalCents: true },
          _count: { id: true },
        }),
      ]);

      // Buscar detalhes dos produtos top
      const topProductsWithDetails = await Promise.all(
        topProducts.map(async (product) => {
          const productDetails = await db.product.findUnique({
            where: { id: product.productId },
            select: {
              name: true,
              description: true,
              priceCents: true,
              status: true,
              thumbnail: true,
            },
          });
          return {
            id: product.productId,
            name: productDetails?.name || "Produto não encontrado",
            description: productDetails?.description,
            image: productDetails?.thumbnail,
            price: productDetails?.priceCents || 0,
            sales: product._count.id,
            revenue: product._sum.totalCents || 0,
            status: productDetails?.status?.toLowerCase() as "published" | "draft" | "archived" || "draft",
            change: Math.random() * 20 - 10, // Simulado por enquanto
          };
        })
      );

      // Processar atividades recentes
      const activities = recentOrders.map((order) => ({
        id: order.id,
        type: "sale" as const,
        title: `Nova venda: ${order.product?.name || "Produto"}`,
        description: `Compra realizada por ${order.buyer?.name || "Cliente"}`,
        amount: order.totalCents,
        status: order.status === "COMPLETED" ? "completed" as const :
                order.status === "PENDING" ? "pending" as const : "failed" as const,
        timestamp: order.createdAt.toISOString(),
        user: order.buyer ? {
          name: order.buyer.name,
          email: order.buyer.email,
        } : undefined,
      }));

      // Processar dados dos gráficos
      const revenueChart = revenueChartData.map((item) => {
        const date = new Date(item.createdAt);
        return {
          month: date.toLocaleDateString("pt-BR", { month: "short", year: "numeric" }),
          revenue: item._sum.totalCents || 0,
          sales: item._count.id,
        };
      });

      const salesChart = salesChartData.map((item) => {
        const date = new Date(item.createdAt);
        return {
          day: date.toLocaleDateString("pt-BR", { day: "2-digit", month: "short" }),
          sales: item._count.id,
          revenue: item._sum.totalCents || 0,
        };
      });

      // Calcular mudanças percentuais
      const revenueChange = lastMonthRevenue._sum.totalCents && monthlyRevenue._sum.totalCents
        ? ((monthlyRevenue._sum.totalCents - lastMonthRevenue._sum.totalCents) / lastMonthRevenue._sum.totalCents) * 100
        : 0;

      const salesChange = lastMonthSales && monthlySales
        ? ((monthlySales - lastMonthSales) / lastMonthSales) * 100
        : 0;

      const customersChange = lastMonthCustomers.length && totalCustomers.length
        ? ((totalCustomers.length - lastMonthCustomers.length) / lastMonthCustomers.length) * 100
        : 0;

      const productsChange = lastMonthProducts && totalProducts
        ? ((totalProducts - lastMonthProducts) / lastMonthProducts) * 100
        : 0;

      const publishedProductsChange = lastMonthPublishedProducts && publishedProducts
        ? ((publishedProducts - lastMonthPublishedProducts) / lastMonthPublishedProducts) * 100
        : 0;

      const todaySalesChange = yesterdaySales && todaySales
        ? ((todaySales - yesterdaySales) / yesterdaySales) * 100
        : 0;

      const activeCustomersChange = lastMonthCustomers.length && activeCustomers.length
        ? ((activeCustomers.length - lastMonthCustomers.length) / lastMonthCustomers.length) * 100
        : 0;

      // Calcular taxa de conversão (simulada)
      const conversionRate = totalSales > 0 ? (totalSales / (totalSales * 20)) * 100 : 0;
      const conversionChange = Math.random() * 10 - 5; // Simulado

      // Calcular score de performance (simulado baseado em métricas reais)
      const performanceScore = Math.min(100, Math.max(0,
        (conversionRate * 0.3) +
        (Math.min(revenueChange, 50) * 0.3) +
        (Math.min(salesChange, 50) * 0.2) +
        (Math.min(customersChange, 50) * 0.2)
      ));
      const performanceChange = Math.random() * 10 - 5; // Simulado

      return c.json({
        metrics: {
          revenue: {
            total: totalRevenue._sum.totalCents || 0,
            change: revenueChange,
            monthly: monthlyRevenue._sum.totalCents || 0,
            monthlyChange: revenueChange,
          },
          sales: {
            total: totalSales,
            change: salesChange,
            today: todaySales,
            todayChange: todaySalesChange,
          },
          customers: {
            total: totalCustomers.length,
            change: customersChange,
            active: activeCustomers.length,
            activeChange: activeCustomersChange,
          },
          products: {
            total: totalProducts,
            change: productsChange,
            published: publishedProducts,
            publishedChange: publishedProductsChange,
          },
          conversion: {
            rate: conversionRate,
            change: conversionChange,
          },
          performance: {
            score: performanceScore,
            change: performanceChange,
          },
        },
        activities,
        topProducts: topProductsWithDetails,
        charts: {
          revenueChart,
          salesChart,
        },
      });
    },
  );

export type DashboardRouter = typeof dashboardRouter;
