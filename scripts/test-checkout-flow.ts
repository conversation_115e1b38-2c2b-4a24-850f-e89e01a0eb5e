import { db } from "@repo/database/prisma/client";

async function testCheckoutFlow() {
  console.log("🔍 Verificando produtos no banco de dados...");

  try {
    // Buscar produtos publicados
    const products = await db.product.findMany({
      where: {
        status: 'PUBLISHED',
      },
      include: {
        organization: {
          select: {
            name: true,
            slug: true,
          }
        },
        category: {
          select: {
            name: true,
          }
        },
        _count: {
          select: {
            orders: true,
            enrollments: true,
          },
        },
      },
      take: 5,
    });

    console.log(`✅ Encontrados ${products.length} produtos publicados:`);

    products.forEach((product, index) => {
      console.log(`\n${index + 1}. ${product.name}`);
      console.log(`   ID: ${product.id}`);
      console.log(`   Tipo: ${product.type}`);
      console.log(`   Preço: R$ ${(product.priceCents / 100).toFixed(2)}`);
      console.log(`   Status: ${product.status}`);
      console.log(`   Organização: ${product.organization.name}`);
      console.log(`   Vendas: ${product._count.orders}`);
      console.log(`   Alunos: ${product._count.enrollments}`);
      console.log(`   URL Pública: http://localhost:3000/products/${product.id}`);
      console.log(`   URL Checkout: http://localhost:3000/checkout/${product.id}`);
    });

    if (products.length === 0) {
      console.log("\n❌ Nenhum produto publicado encontrado!");
      console.log("💡 Para testar o checkout, você precisa:");
      console.log("   1. Criar um produto no painel administrativo");
      console.log("   2. Publicar o produto (status: PUBLISHED)");
      console.log("   3. Acessar a URL pública do produto");
    } else {
      console.log("\n🎉 Fluxo de checkout está pronto para teste!");
      console.log("📝 Próximos passos:");
      console.log("   1. Acesse uma das URLs públicas listadas acima");
      console.log("   2. Clique no botão 'Comprar Agora'");
      console.log("   3. Teste o processo de checkout");
    }

  } catch (error) {
    console.error("❌ Erro ao verificar produtos:", error);
  } finally {
    await db.$disconnect();
  }
}

testCheckoutFlow();
