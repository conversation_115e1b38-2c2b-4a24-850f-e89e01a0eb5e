import { PrismaClient } from "@repo/database";
import { faker } from "@faker-js/faker";

const prisma = new PrismaClient();

// Configurar faker para português brasileiro
faker.locale = "pt_BR";

const ORGANIZATION_ID = "cmf6bztpm0000yoa2jf0nnugg"; // ID da organização existente
const CREATOR_ID = "cmf6bztpm0000yoa2jf0nnugg"; // ID do criador (assumindo que é o mesmo)

// Dados de exemplo para produtos
const PRODUCTS_DATA = [
  {
    name: "Curso Completo de React e Next.js",
    slug: "curso-completo-react-nextjs",
    description: "Aprenda React e Next.js do zero ao avançado com projetos práticos",
    shortDescription: "Curso completo de React e Next.js",
    priceCents: 29700, // R$ 297,00
    type: "COURSE" as const,
    status: "PUBLISHED" as const,
    visibility: "PUBLIC" as const,
    language: "pt-BR",
    certificate: true,
    downloadable: false,
    features: [
      "50+ aulas em vídeo",
      "Projetos práticos",
      "Certificado de conclusão",
      "Suporte via Discord",
      "Atualizações gratuitas"
    ],
    requirements: [
      "Conhecimento básico de JavaScript",
      "Node.js instalado",
      "Editor de código (VS Code recomendado)"
    ],
    tags: ["react", "nextjs", "javascript", "frontend", "web-development"]
  },
  {
    name: "E-book: Guia Definitivo de TypeScript",
    slug: "ebook-guia-definitivo-typescript",
    description: "O guia mais completo sobre TypeScript para desenvolvedores",
    shortDescription: "Guia completo de TypeScript",
    priceCents: 4900, // R$ 49,00
    type: "EBOOK" as const,
    status: "PUBLISHED" as const,
    visibility: "PUBLIC" as const,
    language: "pt-BR",
    certificate: false,
    downloadable: true,
    features: [
      "200+ páginas",
      "Exemplos práticos",
      "PDF otimizado",
      "Atualizações gratuitas"
    ],
    requirements: [
      "Conhecimento básico de JavaScript"
    ],
    tags: ["typescript", "javascript", "programming", "ebook"]
  },
  {
    name: "Mentoria Individual de Carreira Tech",
    slug: "mentoria-individual-carreira-tech",
    description: "Mentoria personalizada para acelerar sua carreira na área de tecnologia",
    shortDescription: "Mentoria personalizada em carreira tech",
    priceCents: 15000, // R$ 150,00
    type: "MENTORSHIP" as const,
    status: "PUBLISHED" as const,
    visibility: "PUBLIC" as const,
    language: "pt-BR",
    certificate: false,
    downloadable: false,
    features: [
      "Sessão de 1 hora",
      "Plano de carreira personalizado",
      "Revisão de portfólio",
      "Dicas de entrevistas"
    ],
    requirements: [
      "Disponibilidade para sessão online"
    ],
    tags: ["mentoria", "carreira", "tech", "desenvolvimento"]
  },
  {
    name: "Assinatura Premium - Acesso Total",
    slug: "assinatura-premium-acesso-total",
    description: "Acesso completo a todos os cursos e conteúdos da plataforma",
    shortDescription: "Acesso total à plataforma",
    priceCents: 9900, // R$ 99,00
    type: "SUBSCRIPTION" as const,
    status: "PUBLISHED" as const,
    visibility: "PUBLIC" as const,
    language: "pt-BR",
    certificate: true,
    downloadable: true,
    features: [
      "Acesso a todos os cursos",
      "Novos conteúdos mensais",
      "Comunidade exclusiva",
      "Suporte prioritário"
    ],
    requirements: [],
    tags: ["assinatura", "premium", "acesso-total"]
  },
  {
    name: "Bundle: Full Stack Developer",
    slug: "bundle-full-stack-developer",
    description: "Pacote completo para se tornar um desenvolvedor full stack",
    shortDescription: "Pacote completo full stack",
    priceCents: 49700, // R$ 497,00
    type: "BUNDLE" as const,
    status: "PUBLISHED" as const,
    visibility: "PUBLIC" as const,
    language: "pt-BR",
    certificate: true,
    downloadable: false,
    features: [
      "5 cursos completos",
      "Projetos práticos",
      "Certificados",
      "Suporte vitalício"
    ],
    requirements: [
      "Conhecimento básico de programação"
    ],
    tags: ["bundle", "full-stack", "desenvolvimento", "completo"]
  }
];

// Dados de exemplo para usuários/clientes
const CUSTOMERS_DATA = [
  { name: "João Silva", email: "<EMAIL>" },
  { name: "Maria Santos", email: "<EMAIL>" },
  { name: "Pedro Costa", email: "<EMAIL>" },
  { name: "Ana Oliveira", email: "<EMAIL>" },
  { name: "Carlos Lima", email: "<EMAIL>" },
  { name: "Fernanda Rocha", email: "<EMAIL>" },
  { name: "Rafael Alves", email: "<EMAIL>" },
  { name: "Juliana Pereira", email: "<EMAIL>" },
  { name: "Lucas Ferreira", email: "<EMAIL>" },
  { name: "Camila Souza", email: "<EMAIL>" },
  { name: "Diego Martins", email: "<EMAIL>" },
  { name: "Patricia Nunes", email: "<EMAIL>" },
  { name: "Bruno Rodrigues", email: "<EMAIL>" },
  { name: "Larissa Gomes", email: "<EMAIL>" },
  { name: "Thiago Barbosa", email: "<EMAIL>" }
];

// Métodos de pagamento
const PAYMENT_METHODS = ["PIX", "CREDIT_CARD", "BOLETO", "DEBIT_CARD"];

// Status de pedidos
const ORDER_STATUSES = ["COMPLETED", "PENDING", "CANCELLED", "FAILED"] as const;

async function createUsers() {
  console.log("🔄 Criando usuários...");

  const users = [];
  for (const customerData of CUSTOMERS_DATA) {
    const user = await prisma.user.upsert({
      where: { email: customerData.email },
      update: {},
      create: {
        name: customerData.name,
        email: customerData.email,
        emailVerified: true,
        role: "CUSTOMER",
        onboardingComplete: true,
        locale: "pt-BR"
      }
    });
    users.push(user);
  }

  console.log(`✅ ${users.length} usuários criados`);
  return users;
}

async function createProducts() {
  console.log("🔄 Criando produtos...");

  const products = [];
  for (const productData of PRODUCTS_DATA) {
    const product = await prisma.product.upsert({
      where: {
        organizationId_slug: {
          organizationId: ORGANIZATION_ID,
          slug: productData.slug
        }
      },
      update: {},
      create: {
        organizationId: ORGANIZATION_ID,
        creatorId: CREATOR_ID,
        ...productData
      }
    });
    products.push(product);
  }

  console.log(`✅ ${products.length} produtos criados`);
  return products;
}

async function createOrders(users: any[], products: any[]) {
  console.log("🔄 Criando pedidos...");

  const orders = [];
  const now = new Date();

  // Criar pedidos dos últimos 90 dias
  for (let i = 0; i < 150; i++) {
    const randomUser = faker.helpers.arrayElement(users);
    const randomProduct = faker.helpers.arrayElement(products);
    const randomStatus = faker.helpers.arrayElement(ORDER_STATUSES);
    const randomPaymentMethod = faker.helpers.arrayElement(PAYMENT_METHODS);

    // Data aleatória nos últimos 90 dias
    const createdAt = faker.date.between({
      from: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
      to: now
    });

    const order = await prisma.order.create({
      data: {
        organizationId: ORGANIZATION_ID,
        buyerId: randomUser.id,
        productId: randomProduct.id,
        status: randomStatus,
        type: "PURCHASE",
        totalCents: randomProduct.priceCents,
        currency: "BRL",
        paymentMethod: randomPaymentMethod,
        paymentId: faker.string.alphanumeric(20),
        createdAt,
        updatedAt: createdAt
      }
    });

    orders.push(order);
  }

  console.log(`✅ ${orders.length} pedidos criados`);
  return orders;
}

async function createReviews(users: any[], products: any[]) {
  console.log("🔄 Criando avaliações...");

  const reviews = [];

  for (const product of products) {
    // Criar 3-8 avaliações por produto
    const numReviews = faker.number.int({ min: 3, max: 8 });

    for (let i = 0; i < numReviews; i++) {
      const randomUser = faker.helpers.arrayElement(users);

      const review = await prisma.productReview.upsert({
        where: {
          userId_productId: {
            userId: randomUser.id,
            productId: product.id
          }
        },
        update: {},
        create: {
          userId: randomUser.id,
          productId: product.id,
          rating: faker.number.int({ min: 3, max: 5 }),
          comment: faker.lorem.sentences(2),
          createdAt: faker.date.between({
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            to: new Date()
          })
        }
      });

      reviews.push(review);
    }
  }

  console.log(`✅ ${reviews.length} avaliações criadas`);
  return reviews;
}

async function createCategories() {
  console.log("🔄 Criando categorias...");

  const categories = [
    { name: "Desenvolvimento Web", slug: "desenvolvimento-web", description: "Cursos de desenvolvimento web" },
    { name: "JavaScript", slug: "javascript", description: "Cursos de JavaScript e frameworks" },
    { name: "React", slug: "react", description: "Cursos de React e ecossistema" },
    { name: "Node.js", slug: "nodejs", description: "Cursos de Node.js e backend" },
    { name: "E-books", slug: "ebooks", description: "Livros digitais e guias" },
    { name: "Mentoria", slug: "mentoria", description: "Sessões de mentoria personalizada" }
  ];

  const createdCategories = [];
  for (const categoryData of categories) {
    const category = await prisma.category.upsert({
      where: {
        organizationId_slug: {
          organizationId: ORGANIZATION_ID,
          slug: categoryData.slug
        }
      },
      update: {},
      create: {
        organizationId: ORGANIZATION_ID,
        ...categoryData
      }
    });
    createdCategories.push(category);
  }

  console.log(`✅ ${createdCategories.length} categorias criadas`);
  return createdCategories;
}

async function createCoupons() {
  console.log("🔄 Criando cupons...");

  const coupons = [
    {
      code: "BEMVINDO20",
      type: "PERCENTAGE",
      valueCents: 2000, // 20%
      minAmountCents: 5000, // R$ 50,00
      maxUses: 100,
      isActive: true,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 dias
    },
    {
      code: "DESCONTO50",
      type: "FIXED",
      valueCents: 5000, // R$ 50,00
      minAmountCents: 10000, // R$ 100,00
      maxUses: 50,
      isActive: true,
      expiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000) // 15 dias
    },
    {
      code: "BLACKFRIDAY",
      type: "PERCENTAGE",
      valueCents: 3000, // 30%
      minAmountCents: 0,
      maxUses: 200,
      isActive: true,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 dias
    }
  ];

  const createdCoupons = [];
  for (const couponData of coupons) {
    const coupon = await prisma.coupon.upsert({
      where: {
        organizationId_code: {
          organizationId: ORGANIZATION_ID,
          code: couponData.code
        }
      },
      update: {},
      create: {
        organizationId: ORGANIZATION_ID,
        creatorId: CREATOR_ID,
        ...couponData
      }
    });
    createdCoupons.push(coupon);
  }

  console.log(`✅ ${createdCoupons.length} cupons criados`);
  return createdCoupons;
}

async function createAffiliateProfiles(users: any[]) {
  console.log("🔄 Criando perfis de afiliados...");

  const affiliateUsers = users.slice(0, 5); // Primeiros 5 usuários como afiliados
  const profiles = [];

  for (const user of affiliateUsers) {
    const profile = await prisma.affiliateProfile.upsert({
      where: { userId: user.id },
      update: {},
      create: {
        userId: user.id,
        commissionCents: faker.number.int({ min: 1000, max: 5000 }), // R$ 10,00 - R$ 50,00
        totalEarningsCents: faker.number.int({ min: 0, max: 50000 }), // Até R$ 500,00
        currency: "BRL",
        isActive: true,
        bankAccount: {
          bank: faker.company.name(),
          agency: faker.string.numeric(4),
          account: faker.string.numeric(8),
          type: "CHECKING"
        }
      }
    });
    profiles.push(profile);
  }

  console.log(`✅ ${profiles.length} perfis de afiliados criados`);
  return profiles;
}

async function createTransactions(orders: any[]) {
  console.log("🔄 Criando transações...");

  const transactions = [];

  for (const order of orders) {
    if (order.status === "COMPLETED") {
      const transaction = await prisma.transaction.create({
        data: {
          organizationId: ORGANIZATION_ID,
          type: "CREDIT",
          status: "COMPLETED",
          amountCents: order.totalCents,
          currency: "BRL",
          description: `Pagamento do pedido ${order.id}`,
          externalId: faker.string.alphanumeric(20),
          paymentMethod: order.paymentMethod,
          toUserId: CREATOR_ID,
          orderId: order.id,
          processedAt: order.createdAt,
          settledAt: new Date(order.createdAt.getTime() + 24 * 60 * 60 * 1000) // 1 dia depois
        }
      });
      transactions.push(transaction);
    }
  }

  console.log(`✅ ${transactions.length} transações criadas`);
  return transactions;
}

async function createLedgerEntries(orders: any[], transactions: any[]) {
  console.log("🔄 Criando entradas do ledger...");

  const ledgerEntries = [];

  for (let i = 0; i < orders.length; i++) {
    const order = orders[i];
    const transaction = transactions[i];

    if (order.status === "COMPLETED" && transaction) {
      // Entrada de venda bruta
      const saleEntry = await prisma.ledgerEntry.create({
        data: {
          organizationId: ORGANIZATION_ID,
          userId: CREATOR_ID,
          type: "SALE_GROSS",
          amountCents: order.totalCents,
          currency: "BRL",
          description: `Venda do produto ${order.productId}`,
          orderId: order.id,
          transactionId: transaction.id,
          availableAt: order.createdAt,
          settledAt: transaction.settledAt
        }
      });
      ledgerEntries.push(saleEntry);

      // Entrada de taxa da plataforma (5%)
      const platformFee = Math.round(order.totalCents * 0.05);
      const feeEntry = await prisma.ledgerEntry.create({
        data: {
          organizationId: ORGANIZATION_ID,
          userId: CREATOR_ID,
          type: "PLATFORM_FEE",
          amountCents: -platformFee,
          currency: "BRL",
          description: `Taxa da plataforma (5%)`,
          orderId: order.id,
          transactionId: transaction.id,
          availableAt: order.createdAt,
          settledAt: transaction.settledAt
        }
      });
      ledgerEntries.push(feeEntry);
    }
  }

  console.log(`✅ ${ledgerEntries.length} entradas do ledger criadas`);
  return ledgerEntries;
}

async function main() {
  console.log("🚀 Iniciando seed do dashboard...");

  try {
    // Verificar se a organização existe
    const organization = await prisma.organization.findUnique({
      where: { id: ORGANIZATION_ID }
    });

    if (!organization) {
      console.error("❌ Organização não encontrada. Execute primeiro o seed básico.");
      return;
    }

    console.log(`✅ Organização encontrada: ${organization.name}`);

    // Criar dados
    const users = await createUsers();
    const products = await createProducts();
    const categories = await createCategories();
    const coupons = await createCoupons();
    const affiliateProfiles = await createAffiliateProfiles(users);
    const orders = await createOrders(users, products);
    const reviews = await createReviews(users, products);
    const transactions = await createTransactions(orders);
    const ledgerEntries = await createLedgerEntries(orders, transactions);

    // Estatísticas finais
    console.log("\n📊 Estatísticas do seed:");
    console.log(`👥 Usuários: ${users.length}`);
    console.log(`📦 Produtos: ${products.length}`);
    console.log(`📋 Categorias: ${categories.length}`);
    console.log(`🎫 Cupons: ${coupons.length}`);
    console.log(`🤝 Afiliados: ${affiliateProfiles.length}`);
    console.log(`🛒 Pedidos: ${orders.length}`);
    console.log(`⭐ Avaliações: ${reviews.length}`);
    console.log(`💳 Transações: ${transactions.length}`);
    console.log(`📝 Entradas do Ledger: ${ledgerEntries.length}`);

    // Calcular receita total
    const totalRevenue = orders
      .filter(order => order.status === "COMPLETED")
      .reduce((sum, order) => sum + order.totalCents, 0);

    console.log(`💰 Receita total: R$ ${(totalRevenue / 100).toFixed(2)}`);

    console.log("\n✅ Seed do dashboard concluído com sucesso!");
    console.log("🎉 Agora você pode visualizar o dashboard com dados realistas!");

  } catch (error) {
    console.error("❌ Erro durante o seed:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
